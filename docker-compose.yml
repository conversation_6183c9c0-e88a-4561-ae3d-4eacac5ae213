services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "4645:4645"
    environment:
      - NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4644/graphql
      - NODE_ENV=development
    depends_on:
      - backend
    networks:
      - monitor-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "4644:4644"
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/crypto-monitor
      - FRONTEND_URL=http://localhost:4645
      - NODE_ENV=development
    networks:
      - monitor-network

volumes:
  mongodb_data:

networks:
  monitor-network:
    driver: bridge
# Crypto Monitor Terminal

A terminal-style cryptocurrency monitoring application with real-time technical indicators and a cool retro interface inspired by [hnterm.ggerganov.com](https://hnterm.ggerganov.com/).

## Features

### Frontend (Next.js + TypeScript)
- 🖥️ **Terminal-style interface** with retro green-on-black theme
- 📊 **Real-time dashboard** showing technical indicators
- ⌨️ **Keyboard shortcuts** (Ctrl+M to toggle menu)
- 🔍 **Asset search** from OKX token pool
- 📈 **Technical indicators**: RSI, SMA, EMA, Awesome Oscillator
- ⏱️ **Multiple timeframes**: 15m, 1h, 4h, 1d, 1w
- 🎯 **Buy/Sell signals** with clear visual indicators
- 💻 **Command-line interface** for power users

### Backend (NestJS + GraphQL)
- 🚀 **NestJS framework** with GraphQL API
- 🗄️ **MongoDB integration** for data persistence
- 📡 **OKX API integration** for real-time crypto data
- 🧮 **Technical indicator calculations**
- 🔄 **Real-time data fetching** and caching

## Project Structure

```
crypto-monitor/
├── frontend/          # Next.js frontend application
│   ├── src/
│   │   ├── app/       # Next.js app router
│   │   ├── components/ # React components
│   │   └── lib/       # GraphQL client and utilities
├── backend/           # NestJS backend application
│   ├── src/
│   │   ├── crypto/    # Crypto data management
│   │   ├── indicators/ # Technical indicator calculations
│   │   └── graphql/   # GraphQL schema and resolvers
└── README.md
```

## Quick Start

### Prerequisites
- Node.js 18+ 
- MongoDB (local or cloud)
- npm or yarn

### 1. Clone and Setup
```bash
git clone <repository-url>
cd crypto-monitor
```

### 2. Backend Setup
```bash
cd backend
npm install
cp .env.example .env  # Configure your MongoDB URI
npm run start:dev
```

### 3. Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

### 4. Access the Application
- Frontend: http://localhost:3000
- Backend GraphQL Playground: http://localhost:3001/graphql

## Usage

### Terminal Commands
- `help` - Show available commands
- `add <symbol>` - Add crypto asset to watchlist (e.g., `add BTC-USDT`)
- `remove <symbol>` - Remove asset from watchlist
- `list` - List watched assets
- `interval <time>` - Set time interval (15m, 1h, 4h, 1d, 1w)
- `indicators` - Show available indicators
- `fetch <symbol>` - Fetch latest price data
- `clear` - Clear terminal
- `menu` - Toggle side menu

### Keyboard Shortcuts
- `Ctrl+M` - Toggle side menu
- `Escape` - Close menu
- `↑/↓` - Navigate command history

### Available Indicators
- **RSI** - Relative Strength Index (14 period)
- **SMA_20** - Simple Moving Average (20 period)
- **EMA_12** - Exponential Moving Average (12 period)
- **AO** - Awesome Oscillator

### Signal Interpretation
- 🟢 **BUY** - Bullish signal
- 🔴 **SELL** - Bearish signal  
- 🟡 **NEUTRAL** - No clear signal

## Technical Details

### Backend Architecture
- **NestJS** with modular architecture
- **GraphQL** for flexible API queries
- **MongoDB** with Mongoose ODM
- **OKX API** integration for market data
- **Technical indicators** calculated server-side

### Frontend Architecture
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Apollo Client** for GraphQL
- **Tailwind CSS** for styling
- **Lucide React** for icons

### Data Flow
1. Frontend requests available assets from OKX via backend
2. User adds assets to watchlist (stored in MongoDB)
3. Backend fetches price data from OKX API
4. Technical indicators calculated server-side
5. Real-time updates via GraphQL subscriptions
6. Frontend displays signals in terminal-style dashboard

## Development

### Backend Development
```bash
cd backend
npm run start:dev    # Development mode with hot reload
npm run build        # Production build
npm run start:prod   # Production mode
```

### Frontend Development
```bash
cd frontend
npm run dev          # Development mode
npm run build        # Production build
npm run start        # Production mode
```

### Database Setup
Make sure MongoDB is running locally or configure a cloud connection in `.env`:
```
MONGODB_URI=mongodb://localhost:27017/crypto-monitor
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Acknowledgments

- Inspired by [hnterm.ggerganov.com](https://hnterm.ggerganov.com/)
- OKX API for cryptocurrency data
- NestJS and Next.js communities

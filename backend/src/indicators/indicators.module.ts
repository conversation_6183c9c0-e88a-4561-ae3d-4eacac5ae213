import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { IndicatorsService } from './indicators.service';
import { IndicatorsResolver } from './indicators.resolver';
import { IndicatorsSchedulerService } from './indicators-scheduler.service';
import { AdvancedSignalsService } from './advanced-signals.service';
import { AltcoinMarketService } from './altcoin-market.service';
import { CryptoModule } from '../crypto/crypto.module';
import { Asset, AssetSchema } from '../crypto/schemas/asset.schema';
import {
  PriceData,
  PriceDataSchema,
} from '../crypto/schemas/price-data.schema';
import { WebSocketGateway } from '../websocket/websocket.gateway';

@Module({
  imports: [
    CryptoModule,
    MongooseModule.forFeature([
      { name: Asset.name, schema: AssetSchema },
      { name: PriceData.name, schema: PriceDataSchema },
    ]),
  ],
  providers: [
    IndicatorsService,
    IndicatorsResolver,
    IndicatorsSchedulerService,
    AdvancedSignalsService,
    AltcoinMarketService,
    WebSocketGateway,
  ],
  exports: [IndicatorsService, IndicatorsSchedulerService],
})
export class IndicatorsModule {}

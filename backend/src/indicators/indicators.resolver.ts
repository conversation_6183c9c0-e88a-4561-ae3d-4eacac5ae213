import {Args, Field, ObjectType, Query, Resolver} from '@nestjs/graphql';
import {IndicatorsService} from './indicators.service';
import {AltcoinMarketService} from './altcoin-market.service';
import GraphQLJSON from 'graphql-type-json';

@ObjectType()
export class IndicatorResultType {
    @Field()
    symbol: string;

    @Field()
    indicator: string;

    @Field()
    value: number;

    @Field()
    signal: string;

    @Field()
    timestamp: Date;
}

@ObjectType()
export class AltcoinMarketDataType {
    @Field({nullable: true})
    btcDominance?: number;

    @Field({nullable: true})
    altcoinSeasonIndex?: number;

    @Field({nullable: true})
    fearGreedIndex?: number;

    @Field({nullable: true})
    marketCapRatio?: number;

    @Field()
    lastUpdate: Date;

    @Field(() => GraphQLJSON)
    signals: any;
}

@ObjectType()
export class RecommendationType {
    @Field()
    recommendation: string;

    @Field()
    confidence: number;
}

@ObjectType()
export class AssetRecommendationType {
    @Field()
    symbol: string;

    @Field()
    recommendation: string;

    @Field()
    confidence: number;

    @Field(() => [IndicatorResultType])
    indicators: IndicatorResultType[];
}

@ObjectType()
export class DashboardRowData {
    @Field()
    symbol: string;

    @Field(() => Number)
    price: number;

    @Field(() => Number)
    change24h: number;

    @Field(() => Number)
    volume24h: number;

    @Field()
    recommendation: string;

    @Field(() => Number)
    confidence: number;

    @Field(() => [IndicatorResultType])
    indicators: IndicatorResultType[];
}

@Resolver()
export class IndicatorsResolver {
    constructor(
        private readonly indicatorsService: IndicatorsService,
        private readonly altcoinMarketService: AltcoinMarketService
    ) {
    }

    @Query(() => [DashboardRowData])
    async dashboardRows(
        @Args('userId', {type: () => String, defaultValue: 'default'}) userId: string,
    ): Promise<DashboardRowData[]> {
        try {
            // Get user settings to determine exchange, indicators, and category
            const userSettings = await this.indicatorsService.userSettingsService.getUserSettings(userId);
            const selectedExchange = userSettings.selectedExchange;
            const indicators = userSettings.selectedIndicators;
            const category = userSettings.selectedCategory;
            const interval = userSettings.selectedInterval;

            // Build query based on category
            let assetQuery: any = {
                exchange: selectedExchange
            };

            if (category !== 'all') {
                assetQuery.category = category
            }

            const assets = await this.indicatorsService['assetModel']
                .find(assetQuery)
                .sort({position: 1})
                .exec();

            const results: DashboardRowData[] = [];

            for (const asset of assets) {
                try {
                    const symbol = asset.symbol;

                    if (!asset) {
                        console.warn(`⚠️ No asset data found for ${symbol}`);
                        continue;
                    }

                    // Get pre-calculated dashboard data for this interval
                    const dashboardData = asset.dashboardData?.[interval];
                    const cachedIndicators = asset.indicators?.[interval];

                    if (!dashboardData || !cachedIndicators) {
                        console.warn(`⚠️ No pre-calculated data found for ${symbol} ${interval} (exchange: ${asset.exchange})`);
                        continue;
                    }

                    // Helper function to get indicator data (handles both old and new format)
                    const getIndicatorData = (indicatorName: string) => {
                        const indicator = cachedIndicators[indicatorName];
                        if (!indicator) return null;

                        // New format: { value, signal }
                        if (typeof indicator === 'object' && indicator.value !== undefined && indicator.signal !== undefined) {
                            return {value: indicator.value, signal: indicator.signal};
                        }

                        // Old format: fallback to raw value with signal calculation
                        const rawValue = cachedIndicators[`${indicatorName}_VALUE`] || indicator;
                        if (rawValue !== null && rawValue !== undefined) {
                            const signal = this.indicatorsService.generateSignal(indicatorName, rawValue);
                            return {value: rawValue, signal};
                        }

                        return null;
                    };

                    // Build indicator results from cached data (using stored signals when available)
                    const indicatorResults: any[] = [];
                    for (const indicator of indicators) {
                        const indicatorData = getIndicatorData(indicator);
                        if (indicatorData) {
                            indicatorResults.push({
                                symbol,
                                indicator,
                                value: indicatorData.value,
                                signal: indicatorData.signal, // Use stored signal instead of recalculating
                                timestamp: asset.lastIndicatorUpdate || new Date(),
                            });
                        }
                    }

                    results.push({
                        symbol,
                        price: dashboardData.price || 0,
                        change24h: dashboardData.change24h || 0,
                        volume24h: dashboardData.volume24h || 0,

                        recommendation: dashboardData.recommendation || 'HOLD',
                        confidence: dashboardData.confidence || 0,
                        indicators: indicatorResults,
                    });

                } catch (error) {
                    console.error(`❌ Error processing ${asset.symbol}:`, error);
                    // Skip failed assets rather than adding incomplete data
                    continue;
                }
            }

            return results;
        } catch (error) {
            console.error('❌ Error in optimized dashboardRows query:', error);
            return [];
        }
    }

    @Query(() => AltcoinMarketDataType, {nullable: true})
    async altcoinMarketData(): Promise<AltcoinMarketDataType | null> {
        try {
            const marketData = this.altcoinMarketService.getCurrentMarketData();
            if (!marketData) return null;

            return {
                btcDominance: marketData.btcDominance ?? undefined,
                altcoinSeasonIndex: marketData.altcoinSeasonIndex ?? undefined,
                fearGreedIndex: marketData.fearGreedIndex ?? undefined,
                marketCapRatio: marketData.marketCapRatio ?? undefined,
                lastUpdate: marketData.lastUpdate,
                signals: marketData.signals
            };
        } catch (error) {
            console.error('❌ Error fetching altcoin market data:', error);
            return null;
        }
    }
}

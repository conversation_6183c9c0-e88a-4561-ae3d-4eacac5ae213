import {Injectable, Logger} from '@nestjs/common';
import {InjectModel} from '@nestjs/mongoose';
import {Model} from 'mongoose';
import {OKXService} from '../crypto/okx.service';
import {PriceData} from '../crypto/schemas/price-data.schema';
import {Asset} from '../crypto/schemas/asset.schema';
import {UserSettingsService} from '../crypto/user-settings.service';
import {
    ADX,
    AwesomeOscillator,
    BollingerBands,
    CCI,
    EMA,
    MACD,
    MFI,
    OBV,
    RSI,
    SMA,
    Stochastic,
    WilliamsR
} from 'technicalindicators';
import {AdvancedSignalsService} from './advanced-signals.service';

export interface IndicatorResult {
    symbol: string;
    indicator: string;
    value: number;
    signal: 'BUY' | 'SELL' | 'NEUTRAL';
    timestamp: Date;
}

@Injectable()
export class IndicatorsService {
    private readonly logger = new Logger(IndicatorsService.name);

    constructor(
        private readonly okxService: OKXService,
        @InjectModel(Asset.name) private readonly assetModel: Model<Asset>,
        private readonly advancedSignalsService: AdvancedSignalsService,
        public readonly userSettingsService: UserSettingsService,
    ) {
    }

    // Simple Moving Average using technicalindicators library
    calculateSMA(prices: number[], period: number): number[] {
        try {
            return SMA.calculate({period, values: prices});
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating SMA: ${errorMessage}`);
            return [];
        }
    }

    // Exponential Moving Average using technicalindicators library
    calculateEMA(prices: number[], period: number): number[] {
        try {
            return EMA.calculate({period, values: prices});
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating EMA: ${errorMessage}`);
            return [];
        }
    }

    // Awesome Oscillator using technicalindicators library
    calculateAO(priceData: PriceData[]): number[] {
        try {
            const highs = priceData.map((data) => data.high);
            const lows = priceData.map((data) => data.low);
            return AwesomeOscillator.calculate({
                high: highs,
                low: lows,
                fastPeriod: 5,
                slowPeriod: 34,
            });
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating AO: ${errorMessage}`);
            return [];
        }
    }

    // MACD (Moving Average Convergence Divergence) - Top crypto indicator
    calculateMACD(
        prices: number[],
    ): Array<{ MACD: number; signal: number; histogram: number }> {
        try {
            return MACD.calculate({
                values: prices,
                fastPeriod: 12,
                slowPeriod: 26,
                signalPeriod: 9,
                SimpleMAOscillator: false,
                SimpleMASignal: false,
            }) as Array<{ MACD: number; signal: number; histogram: number }>;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating MACD: ${errorMessage}`);
            return [];
        }
    }

    // Bollinger Bands - Excellent for crypto volatility
    calculateBollingerBands(
        prices: number[],
        period: number = 20,
    ): Array<{ upper: number; middle: number; lower: number; pb: number }> {
        try {
            return BollingerBands.calculate({
                period,
                values: prices,
                stdDev: 2,
            }) as Array<{ upper: number; middle: number; lower: number; pb: number }>;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating Bollinger Bands: ${errorMessage}`);
            return [];
        }
    }

    // Stochastic Oscillator - Enhanced momentum indicator
    calculateStochastic(
        priceData: PriceData[],
        period: number = 14,
    ): Array<{ k: number; d: number }> {
        try {
            const highs = priceData.map((data) => data.high);
            const lows = priceData.map((data) => data.low);
            const closes = priceData.map((data) => data.close);

            return Stochastic.calculate({
                high: highs,
                low: lows,
                close: closes,
                period,
                signalPeriod: 3,
            }) as Array<{ k: number; d: number }>;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating Stochastic: ${errorMessage}`);
            return [];
        }
    }

    // On-Balance Volume - Critical for crypto volume analysis
    calculateOBV(priceData: PriceData[]): number[] {
        try {
            const closes = priceData.map((data) => data.close);
            const volumes = priceData.map((data) => data.volume);

            return OBV.calculate({
                close: closes,
                volume: volumes,
            });
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating OBV: ${errorMessage}`);
            return [];
        }
    }

    // Williams %R using technicalindicators library
    calculateWilliamsR(priceData: PriceData[], period: number = 14): number[] {
        try {
            const highs = priceData.map((data) => data.high);
            const lows = priceData.map((data) => data.low);
            const closes = priceData.map((data) => data.close);

            return WilliamsR.calculate({
                high: highs,
                low: lows,
                close: closes,
                period,
            });
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating Williams %R: ${errorMessage}`);
            return [];
        }
    }

    // Commodity Channel Index using technicalindicators library
    calculateCCI(priceData: PriceData[], period: number = 20): number[] {
        try {
            const highs = priceData.map((data) => data.high);
            const lows = priceData.map((data) => data.low);
            const closes = priceData.map((data) => data.close);

            return CCI.calculate({
                high: highs,
                low: lows,
                close: closes,
                period,
            });
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating CCI: ${errorMessage}`);
            return [];
        }
    }

    // Average Directional Index using technicalindicators library
    calculateADX(priceData: PriceData[], period: number = 14): number[] {
        try {
            const highs = priceData.map((data) => data.high);
            const lows = priceData.map((data) => data.low);
            const closes = priceData.map((data) => data.close);

            const adxResults = ADX.calculate({
                high: highs,
                low: lows,
                close: closes,
                period,
            });

            // ADX returns objects with { adx, pdi, mdi }, we want just the adx values
            return adxResults.map(result => result.adx);
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating ADX: ${errorMessage}`);
            return [];
        }
    }

    // Money Flow Index using technicalindicators library
    calculateMFI(priceData: PriceData[], period: number = 14): number[] {
        try {
            const highs = priceData.map((data) => data.high);
            const lows = priceData.map((data) => data.low);
            const closes = priceData.map((data) => data.close);
            const volumes = priceData.map((data) => data.volume);

            return MFI.calculate({
                high: highs,
                low: lows,
                close: closes,
                volume: volumes,
                period,
            });
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating MFI: ${errorMessage}`);
            return [];
        }
    }

    // VWAP (Volume Weighted Average Price) - custom implementation
    calculateVWAP(priceData: PriceData[]): number[] {
        try {
            const result: number[] = [];
            let cumulativeTPV = 0; // Typical Price * Volume
            let cumulativeVolume = 0;

            for (let i = 0; i < priceData.length; i++) {
                const data = priceData[i];
                const typicalPrice = (data.high + data.low + data.close) / 3;
                const tpv = typicalPrice * data.volume;

                cumulativeTPV += tpv;
                cumulativeVolume += data.volume;

                const vwap = cumulativeTPV / cumulativeVolume;
                result.push(vwap);
            }

            return result;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating VWAP: ${errorMessage}`);
            return [];
        }
    }

    // RSI (Relative Strength Index) using technicalindicators library
    calculateRSI(prices: number[], period: number = 14): number[] {
        try {
            return RSI.calculate({period, values: prices});
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error calculating RSI: ${errorMessage}`);
            return [];
        }
    }

    // Generate signal based on indicator value with enhanced trend analysis
    generateSignal(
        indicator: string,
        value: number,
        previousValue?: number,
        additionalData?: any,
    ): 'BUY' | 'SELL' | 'NEUTRAL' {
        switch (indicator) {
            case 'RSI':
                // RSI overbought/oversold levels
                if (value < 30) return 'BUY'; // Oversold
                if (value > 70) return 'SELL'; // Overbought
                return 'NEUTRAL';

            case 'STOCH':
                // Enhanced Stochastic with trend analysis
                if (previousValue !== undefined) {
                    // Crossover signals in extreme zones (most reliable)
                    if (previousValue >= 80 && value < 80) return 'SELL'; // Exit overbought
                    if (previousValue <= 20 && value > 20) return 'BUY'; // Exit oversold

                    // Momentum signals
                    if (value < 20 && value > previousValue) return 'BUY'; // Rising from oversold
                    if (value > 80 && value < previousValue) return 'SELL'; // Falling from overbought
                }
                // Fallback to absolute levels
                if (value < 20) return 'BUY'; // Oversold
                if (value > 80) return 'SELL'; // Overbought
                return 'NEUTRAL';

            case 'AO':
                // Enhanced Awesome Oscillator with crypto-specific weighting
                // AO is particularly reliable in crypto due to momentum-driven nature
                if (previousValue !== undefined) {
                    const momentum = value - previousValue;
                    const momentumStrength = Math.abs(momentum);

                    // Strong momentum signals (high weight in crypto)
                    if (value > previousValue && value > 0 && momentumStrength > Math.abs(value) * 0.1) {
                        return 'BUY'; // Strong rising bullish momentum (high confidence)
                    }
                    if (value < previousValue && value < 0 && momentumStrength > Math.abs(value) * 0.1) {
                        return 'SELL'; // Strong falling bearish momentum (high confidence)
                    }

                    // Zero line crossovers (very reliable in crypto)
                    if (previousValue <= 0 && value > 0) return 'BUY'; // Bullish zero crossover
                    if (previousValue >= 0 && value < 0) return 'SELL'; // Bearish zero crossover

                    // Trend continuation signals
                    if (value > previousValue && value > 0) return 'BUY'; // Rising and positive
                    if (value < previousValue && value < 0) return 'SELL'; // Falling and negative
                    if (value > previousValue) return 'BUY'; // Rising momentum
                    if (value < previousValue) return 'SELL'; // Falling momentum
                }
                // Fallback to absolute values with crypto-adjusted thresholds
                if (value > 0) return 'BUY'; // Bullish momentum
                if (value < 0) return 'SELL'; // Bearish momentum
                return 'NEUTRAL';

            case 'MACD':
                // Enhanced MACD with trend analysis
                if (additionalData?.signal !== undefined) {
                    // Compare MACD line vs signal line (most accurate)
                    const macdAboveSignal = value > additionalData.signal;
                    const prevMacdAboveSignal = previousValue !== undefined && additionalData.previousSignal !== undefined
                        ? previousValue > additionalData.previousSignal : null;

                    // Crossover signals (strongest)
                    if (prevMacdAboveSignal === false && macdAboveSignal) return 'BUY'; // Bullish crossover
                    if (prevMacdAboveSignal === true && !macdAboveSignal) return 'SELL'; // Bearish crossover

                    // Trend continuation signals
                    if (macdAboveSignal && previousValue !== undefined && value > previousValue) return 'BUY'; // Rising above signal
                    if (!macdAboveSignal && previousValue !== undefined && value < previousValue) return 'SELL'; // Falling below signal

                    // Position-based signals
                    if (macdAboveSignal) return 'BUY'; // MACD above signal (bullish)
                    if (!macdAboveSignal) return 'SELL'; // MACD below signal (bearish)
                } else {
                    // Trend analysis with zero line crossover
                    if (previousValue !== undefined) {
                        if (previousValue <= 0 && value > 0) return 'BUY'; // Bullish zero crossover
                        if (previousValue >= 0 && value < 0) return 'SELL'; // Bearish zero crossover
                        if (value > 0 && value > previousValue) return 'BUY'; // Rising above zero
                        if (value < 0 && value < previousValue) return 'SELL'; // Falling below zero
                    }
                    // Fallback to zero line crossover
                    if (value > 0) return 'BUY'; // MACD above zero (bullish)
                    if (value < 0) return 'SELL'; // MACD below zero (bearish)
                }
                return 'NEUTRAL';

            case 'BB':
                // Bollinger Bands signals (price vs bands)
                if (
                    additionalData?.currentPrice &&
                    additionalData?.upper &&
                    additionalData?.lower
                ) {
                    const {currentPrice, upper, lower} = additionalData;
                    // Primary signals: price touching bands
                    if (currentPrice <= lower) return 'BUY'; // Price at/below lower band (oversold)
                    if (currentPrice >= upper) return 'SELL'; // Price at/above upper band (overbought)
                    // Secondary signals: price vs middle band
                    if (currentPrice > value) return 'BUY'; // Price above middle band (bullish)
                    if (currentPrice < value) return 'SELL'; // Price below middle band (bearish)
                }
                return 'NEUTRAL';

            case 'OBV':
                // On-Balance Volume trend signals
                if (previousValue !== undefined) {
                    if (value > previousValue) return 'BUY'; // OBV increasing (accumulation)
                    if (value < previousValue) return 'SELL'; // OBV decreasing (distribution)
                }
                return 'NEUTRAL';

            case 'EMA_12':
            case 'EMA_26':
                // Enhanced EMA with trend analysis
                if (additionalData?.currentPrice) {
                    const currentPrice = additionalData.currentPrice;
                    const priceAboveEMA = currentPrice > value;

                    // EMA trend analysis
                    if (previousValue !== undefined) {
                        const emaTrend = value > previousValue; // EMA rising

                        // Strong signals: price and EMA aligned
                        if (priceAboveEMA && emaTrend) return 'BUY'; // Price above rising EMA (strong bullish)
                        if (!priceAboveEMA && !emaTrend) return 'SELL'; // Price below falling EMA (strong bearish)

                        // Trend signals
                        if (emaTrend) return 'BUY'; // EMA rising (bullish trend)
                        if (!emaTrend) return 'SELL'; // EMA falling (bearish trend)
                    }

                    // Fallback to price vs EMA
                    if (priceAboveEMA) return 'BUY'; // Price above EMA (bullish)
                    if (!priceAboveEMA) return 'SELL'; // Price below EMA (bearish)
                }
                return 'NEUTRAL';

            case 'WILLIAMS_R':
                // Williams %R overbought/oversold with crypto-specific levels
                if (previousValue !== undefined) {
                    // Crossover signals (most reliable)
                    if (previousValue >= -20 && value < -20) return 'SELL'; // Exit overbought
                    if (previousValue <= -80 && value > -80) return 'BUY'; // Exit oversold

                    // Momentum signals in extreme zones
                    if (value < -80 && value > previousValue) return 'BUY'; // Rising from oversold
                    if (value > -20 && value < previousValue) return 'SELL'; // Falling from overbought
                }
                // Crypto-adjusted levels (more sensitive than traditional markets)
                if (value < -80) return 'BUY'; // Oversold
                if (value > -20) return 'SELL'; // Overbought
                return 'NEUTRAL';

            case 'CCI':
                // Commodity Channel Index with crypto-specific thresholds
                if (previousValue !== undefined) {
                    // Strong momentum signals
                    if (previousValue <= 100 && value > 100) return 'BUY'; // Bullish breakout
                    if (previousValue >= -100 && value < -100) return 'SELL'; // Bearish breakdown

                    // Trend continuation
                    if (value > 100 && value > previousValue) return 'BUY'; // Strong uptrend
                    if (value < -100 && value < previousValue) return 'SELL'; // Strong downtrend
                }
                // Crypto-adjusted thresholds (higher volatility)
                if (value > 150) return 'SELL'; // Extremely overbought
                if (value < -150) return 'BUY'; // Extremely oversold
                if (value > 100) return 'BUY'; // Overbought (potential continuation)
                if (value < -100) return 'SELL'; // Oversold (potential continuation)
                return 'NEUTRAL';

            case 'ADX':
                // Average Directional Index (trend strength)
                // ADX doesn't give buy/sell signals directly, but trend strength
                if (value > 50) return 'BUY'; // Very strong trend (follow momentum)
                if (value > 25) return 'BUY'; // Strong trend
                if (value < 20) return 'NEUTRAL'; // Weak trend (sideways market)
                return 'NEUTRAL';

            case 'MFI':
                // Money Flow Index (volume-weighted RSI)
                if (previousValue !== undefined) {
                    // Crossover signals
                    if (previousValue >= 80 && value < 80) return 'SELL'; // Exit overbought
                    if (previousValue <= 20 && value > 20) return 'BUY'; // Exit oversold

                    // Divergence potential (simplified)
                    if (value < 20 && value > previousValue) return 'BUY'; // Rising from oversold
                    if (value > 80 && value < previousValue) return 'SELL'; // Falling from overbought
                }
                // Standard MFI levels
                if (value < 20) return 'BUY'; // Oversold
                if (value > 80) return 'SELL'; // Overbought
                return 'NEUTRAL';

            case 'VWAP':
                // Volume Weighted Average Price
                if (additionalData?.currentPrice) {
                    const currentPrice = additionalData.currentPrice;
                    const priceVsVWAP = (currentPrice - value) / value;

                    // Price vs VWAP analysis
                    if (priceVsVWAP > 0.02) return 'SELL'; // Price significantly above VWAP (potential reversal)
                    if (priceVsVWAP < -0.02) return 'BUY'; // Price significantly below VWAP (potential bounce)
                    if (currentPrice > value) return 'BUY'; // Price above VWAP (bullish)
                    if (currentPrice < value) return 'SELL'; // Price below VWAP (bearish)
                }
                return 'NEUTRAL';

            default:
                return 'NEUTRAL';
        }
    }

    calculateRecommendation(
        indicatorResults: Array<{ indicator: string; signal: string }>,
    ): {
        recommendation: string;
        confidence: number;
    } {
        if (indicatorResults.length === 0) {
            return {recommendation: 'HOLD', confidence: 0};
        }

        // Enhanced crypto indicator weighting system (research-based with new indicators)
        const indicatorWeights: Record<string, number> = {
            // Core indicators (rebalanced)
            RSI: 0.18, // High weight - excellent for crypto momentum
            MACD: 0.18, // High weight - best trend indicator for crypto
            BB: 0.15, // High weight - perfect for crypto volatility
            STOCH: 0.10, // Medium weight - good momentum confirmation
            AO: 0.16, // ✅ INCREASED: AO is particularly reliable in crypto momentum markets
            OBV: 0.05, // Lower weight - volume confirmation
            EMA_12: 0.02, // Low weight - basic trend
            EMA_26: 0.01, // Low weight - basic trend

            // New crypto-specific indicators (research-based weights)
            WILLIAMS_R: 0.08, // Good for crypto overbought/oversold detection
            CCI: 0.09, // Excellent for crypto cyclical trends
            ADX: 0.12, // Very important for trend strength in volatile crypto markets
            MFI: 0.10, // Volume-weighted RSI - very reliable in crypto
            VWAP: 0.11, // Institutional benchmark - crucial in crypto trading
        };

        let weightedBuyScore = 0;
        let weightedSellScore = 0;
        let totalWeight = 0;

        indicatorResults.forEach((result) => {
            if (result && result.signal && result.indicator) {
                const weight = indicatorWeights[result.indicator] || 0.05; // Default weight for unknown indicators
                totalWeight += weight;

                if (result.signal === 'BUY') {
                    weightedBuyScore += weight;
                } else if (result.signal === 'SELL') {
                    weightedSellScore += weight;
                }
                // NEUTRAL signals don't add to either score
            }
        });

        if (totalWeight === 0) {
            return {recommendation: 'HOLD', confidence: 0};
        }

        const buyPercentage = weightedBuyScore / totalWeight;
        const sellPercentage = weightedSellScore / totalWeight;
        const maxPercentage = Math.max(buyPercentage, sellPercentage);
        const confidence = maxPercentage * 100;

        // Professional crypto thresholds based on research
        if (buyPercentage >= 0.65) {
            return {recommendation: 'BUY', confidence};
        }
        if (sellPercentage >= 0.65) {
            return {recommendation: 'SELL', confidence};
        }
        if (maxPercentage >= 0.45) {
            return {
                recommendation:
                    buyPercentage > sellPercentage ? 'WEAK BUY' : 'WEAK SELL',
                confidence,
            };
        }
        return {recommendation: 'HOLD', confidence};
    }

    /**
     * Enhanced recommendation using advanced signal analysis
     * Combines traditional weighted approach with sophisticated multi-layer analysis
     */
    calculateEnhancedRecommendation(
        indicatorResults: Array<{ indicator: string; signal: string; value: number }>,
    ): {
        recommendation: string;
        confidence: number;
        reasoning?: string;
        riskLevel?: string;
        signals?: any;
        fallbackUsed?: boolean;
    } {
        try {
            // Try advanced analysis first
            const advancedResult = this.advancedSignalsService.calculateAdvancedRecommendation(indicatorResults);

            // If advanced analysis provides high confidence, use it
            return {
                recommendation: advancedResult.recommendation,
                confidence: advancedResult.confidence,
                reasoning: advancedResult.reasoning,
                riskLevel: advancedResult.riskLevel,
                signals: advancedResult.signals,
                fallbackUsed: false
            };
        } catch (error) {
            this.logger.error(`Advanced signals calculation failed: ${error.message}`);

            // Fallback to traditional method
            const fallbackResult = this.calculateRecommendation(
                indicatorResults.map(r => ({indicator: r.indicator, signal: r.signal}))
            );

            return {
                recommendation: fallbackResult.recommendation,
                confidence: fallbackResult.confidence,
                reasoning: 'Traditional analysis (advanced analysis unavailable)',
                riskLevel: 'MEDIUM',
                fallbackUsed: true
            };
        }
    }
}

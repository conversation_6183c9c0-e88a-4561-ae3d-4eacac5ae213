import { Injectable, Logger } from '@nestjs/common';

export interface AdvancedSignalResult {
    recommendation: string;
    confidence: number;
    reasoning: string;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    signals: {
        trend: string;
        momentum: string;
        volume: string;
        volatility: string;
    };
}

@Injectable()
export class AdvancedSignalsService {
    private readonly logger = new Logger(AdvancedSignalsService.name);

    /**
     * Advanced Trading Signal System
     * Based on proven trading strategies and market research
     * Uses multi-layered analysis for actionable buy/sell/hold signals
     */
    calculateAdvancedRecommendation(
        indicatorResults: Array<{ indicator: string; signal: string; value: number }>
    ): AdvancedSignalResult {
        if (indicatorResults.length === 0) {
            return {
                recommendation: 'HOLD',
                confidence: 0,
                reasoning: 'Insufficient data for analysis',
                riskLevel: 'HIGH',
                signals: { trend: 'Unknown', momentum: 'Unknown', volume: 'Unknown', volatility: 'Unknown' }
            };
        }

        // 1. TREND ANALYSIS - Primary signal source
        const trendAnalysis = this.analyzeTrend(indicatorResults);
        
        // 2. MOMENTUM ANALYSIS - Confirms trend strength
        const momentumAnalysis = this.analyzeMomentum(indicatorResults);
        
        // 3. VOLUME ANALYSIS - Validates price movements
        const volumeAnalysis = this.analyzeVolume(indicatorResults);
        
        // 4. VOLATILITY ANALYSIS - Risk assessment
        const volatilityAnalysis = this.analyzeVolatility(indicatorResults);
        
        // 5. CONFLUENCE ANALYSIS - Multiple signal confirmation
        const confluenceScore = this.calculateConfluence(trendAnalysis, momentumAnalysis, volumeAnalysis);
        
        // 6. RISK ASSESSMENT
        const riskLevel = this.assessRisk(volatilityAnalysis, confluenceScore);
        
        // 7. FINAL DECISION MATRIX
        return this.generateFinalRecommendation(
            trendAnalysis, 
            momentumAnalysis, 
            volumeAnalysis, 
            volatilityAnalysis,
            confluenceScore, 
            riskLevel
        );
    }

    private analyzeTrend(indicators: Array<{ indicator: string; signal: string; value: number }>): {
        direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
        strength: number;
        signals: string[];
        altcoinFriendly: boolean;
    } {
        const trendIndicators = ['EMA_12', 'EMA_26', 'MACD', 'ADX'];
        const altcoinIndicators = ['BTC_DOMINANCE', 'ALTCOIN_SEASON', 'MARKET_CAP_RATIO'];
        const signals: string[] = [];
        let bullishCount = 0;
        let bearishCount = 0;
        let adxValue = 0;
        let altcoinBullishCount = 0;
        let altcoinBearishCount = 0;

        indicators.forEach(ind => {
            if (trendIndicators.includes(ind.indicator)) {
                if (ind.signal === 'BUY') {
                    bullishCount++;
                    signals.push(`${ind.indicator}: Bullish trend`);
                } else if (ind.signal === 'SELL') {
                    bearishCount++;
                    signals.push(`${ind.indicator}: Bearish trend`);
                }

                if (ind.indicator === 'ADX') {
                    adxValue = ind.value;
                }
            }

            // Altcoin-specific trend analysis
            if (altcoinIndicators.includes(ind.indicator)) {
                if (ind.signal === 'BUY') {
                    altcoinBullishCount++;
                    signals.push(`${ind.indicator}: Altcoin-friendly environment`);
                } else if (ind.signal === 'SELL') {
                    altcoinBearishCount++;
                    signals.push(`${ind.indicator}: Bitcoin-dominant environment`);
                }
            }
        });

        const totalSignals = bullishCount + bearishCount;
        const altcoinSignals = altcoinBullishCount + altcoinBearishCount;

        // Enhanced trend strength with altcoin market context
        let trendStrength = totalSignals > 0 ?
            Math.min(100, (Math.abs(bullishCount - bearishCount) / totalSignals) * 100 + Math.min(adxValue, 50)) : 0;

        // Boost strength if altcoin environment is favorable
        const altcoinFriendly = altcoinBullishCount > altcoinBearishCount;
        if (altcoinFriendly && altcoinSignals > 0) {
            trendStrength += 15; // Bonus for altcoin-friendly environment
            trendStrength = Math.min(100, trendStrength);
        }

        let direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
        if (bullishCount > bearishCount && trendStrength > 40) {
            direction = 'BULLISH';
        } else if (bearishCount > bullishCount && trendStrength > 40) {
            direction = 'BEARISH';
        } else {
            direction = 'SIDEWAYS';
        }

        return { direction, strength: trendStrength, signals, altcoinFriendly };
    }

    private analyzeMomentum(indicators: Array<{ indicator: string; signal: string; value: number }>): {
        momentum: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';
        strength: number;
        signals: string[];
        extremeConditions: number;
    } {
        const momentumIndicators = ['RSI', 'STOCH', 'AO', 'WILLIAMS_R', 'CCI', 'MFI'];
        const signals: string[] = [];
        let buyCount = 0;
        let sellCount = 0;
        let extremeConditions = 0;

        indicators.forEach(ind => {
            if (momentumIndicators.includes(ind.indicator)) {
                if (ind.signal === 'BUY') {
                    buyCount++;
                    signals.push(`${ind.indicator}: Bullish momentum`);
                    
                    // Extreme oversold = high probability reversal
                    if ((ind.indicator === 'RSI' && ind.value < 25) ||
                        (ind.indicator === 'STOCH' && ind.value < 15) ||
                        (ind.indicator === 'WILLIAMS_R' && ind.value < -85) ||
                        (ind.indicator === 'MFI' && ind.value < 15)) {
                        extremeConditions++;
                    }
                } else if (ind.signal === 'SELL') {
                    sellCount++;
                    signals.push(`${ind.indicator}: Bearish momentum`);
                    
                    // Extreme overbought = high probability reversal
                    if ((ind.indicator === 'RSI' && ind.value > 75) ||
                        (ind.indicator === 'STOCH' && ind.value > 85) ||
                        (ind.indicator === 'WILLIAMS_R' && ind.value > -15) ||
                        (ind.indicator === 'MFI' && ind.value > 85)) {
                        extremeConditions++;
                    }
                }
            }
        });

        const totalSignals = buyCount + sellCount;
        const momentumStrength = totalSignals > 0 ? 
            (Math.abs(buyCount - sellCount) / totalSignals) * 100 + (extremeConditions * 25) : 0;

        let momentum: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';
        if (buyCount > sellCount) {
            momentum = extremeConditions >= 2 ? 'STRONG_BUY' : 'BUY';
        } else if (sellCount > buyCount) {
            momentum = extremeConditions >= 2 ? 'STRONG_SELL' : 'SELL';
        } else {
            momentum = 'NEUTRAL';
        }

        return { momentum, strength: Math.min(100, momentumStrength), signals, extremeConditions };
    }

    private analyzeVolume(indicators: Array<{ indicator: string; signal: string; value: number }>): {
        trend: 'ACCUMULATION' | 'DISTRIBUTION' | 'NEUTRAL';
        strength: number;
        signals: string[];
    } {
        const volumeIndicators = ['OBV', 'MFI', 'VWAP'];
        const signals: string[] = [];
        let accumulationCount = 0;
        let distributionCount = 0;

        indicators.forEach(ind => {
            if (volumeIndicators.includes(ind.indicator)) {
                if (ind.signal === 'BUY') {
                    accumulationCount++;
                    signals.push(`${ind.indicator}: Volume accumulation`);
                } else if (ind.signal === 'SELL') {
                    distributionCount++;
                    signals.push(`${ind.indicator}: Volume distribution`);
                }
            }
        });

        const totalSignals = accumulationCount + distributionCount;
        const volumeStrength = totalSignals > 0 ? 
            (Math.abs(accumulationCount - distributionCount) / totalSignals) * 100 : 0;

        let trend: 'ACCUMULATION' | 'DISTRIBUTION' | 'NEUTRAL';
        if (accumulationCount > distributionCount) {
            trend = 'ACCUMULATION';
        } else if (distributionCount > accumulationCount) {
            trend = 'DISTRIBUTION';
        } else {
            trend = 'NEUTRAL';
        }

        return { trend, strength: volumeStrength, signals };
    }

    private analyzeVolatility(indicators: Array<{ indicator: string; signal: string; value: number }>): {
        level: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
        signals: string[];
    } {
        const signals: string[] = [];
        let volatilityScore = 0;

        indicators.forEach(ind => {
            if (ind.indicator === 'BB') {
                signals.push('BB: Volatility analysis');
                volatilityScore += 1;
            } else if (ind.indicator === 'CCI') {
                if (Math.abs(ind.value) > 200) {
                    volatilityScore += 3;
                    signals.push('CCI: Extreme volatility detected');
                } else if (Math.abs(ind.value) > 100) {
                    volatilityScore += 2;
                    signals.push('CCI: High volatility');
                } else {
                    volatilityScore += 1;
                    signals.push('CCI: Normal volatility');
                }
            }
        });

        let level: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
        if (volatilityScore >= 5) {
            level = 'EXTREME';
        } else if (volatilityScore >= 4) {
            level = 'HIGH';
        } else if (volatilityScore >= 2) {
            level = 'MEDIUM';
        } else {
            level = 'LOW';
        }

        return { level, signals };
    }

    private calculateConfluence(trend: any, momentum: any, volume: any): number {
        let score = 0;

        // Trend-Momentum alignment (40 points max)
        if ((trend.direction === 'BULLISH' && ['BUY', 'STRONG_BUY'].includes(momentum.momentum)) ||
            (trend.direction === 'BEARISH' && ['SELL', 'STRONG_SELL'].includes(momentum.momentum))) {
            score += 40;
        } else if (trend.direction === 'SIDEWAYS' && momentum.momentum === 'NEUTRAL') {
            score += 20;
        }

        // Volume confirmation (30 points max)
        if ((trend.direction === 'BULLISH' && volume.trend === 'ACCUMULATION') ||
            (trend.direction === 'BEARISH' && volume.trend === 'DISTRIBUTION')) {
            score += 30;
        } else if (volume.trend === 'NEUTRAL') {
            score += 10;
        }

        // Signal strength alignment (20 points max)
        if (trend.strength > 60 && momentum.strength > 60) {
            score += 20;
        } else if (trend.strength > 40 && momentum.strength > 40) {
            score += 10;
        }

        // Volume strength (10 points max)
        if (volume.strength > 70) {
            score += 10;
        } else if (volume.strength > 40) {
            score += 5;
        }

        return Math.min(100, score);
    }

    private assessRisk(volatility: any, confluence: number): 'LOW' | 'MEDIUM' | 'HIGH' {
        if (volatility.level === 'EXTREME' || confluence < 30) {
            return 'HIGH';
        } else if (volatility.level === 'HIGH' || confluence < 60) {
            return 'MEDIUM';
        } else {
            return 'LOW';
        }
    }

    private generateFinalRecommendation(
        trend: any,
        momentum: any,
        volume: any,
        volatility: any,
        confluence: number,
        riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
    ): AdvancedSignalResult {
        let recommendation = 'HOLD';
        let confidence = 0;
        let reasoning = '';

        // STRONG BUY: Perfect alignment with extreme oversold conditions
        if (trend.direction === 'BULLISH' &&
            momentum.momentum === 'STRONG_BUY' &&
            volume.trend === 'ACCUMULATION' &&
            confluence >= 80 &&
            momentum.extremeConditions >= 2) {
            recommendation = 'STRONG BUY';
            confidence = Math.min(95, confluence + 10);

            // Enhanced reasoning for altcoins
            if (trend.altcoinFriendly) {
                confidence = Math.min(98, confidence + 5);
                reasoning = `ALTCOIN STRONG BUY: ${momentum.extremeConditions} extreme oversold indicators with trend, volume confirmation, and altcoin-friendly market environment`;
            } else {
                reasoning = `Strong bullish setup: ${momentum.extremeConditions} extreme oversold indicators with trend and volume confirmation`;
            }
        }
        // BUY: Strong bullish alignment
        else if (trend.direction === 'BULLISH' &&
                 ['BUY', 'STRONG_BUY'].includes(momentum.momentum) &&
                 confluence >= 65) {
            recommendation = 'BUY';
            confidence = confluence;

            // Enhanced reasoning for altcoins
            if (trend.altcoinFriendly) {
                confidence = Math.min(95, confidence + 8);
                recommendation = 'STRONG BUY'; // Upgrade to strong buy in altcoin-friendly environment
                reasoning = `ALTCOIN BUY: Bullish trend with ${momentum.momentum.toLowerCase()} momentum, ${Math.round(confluence)}% confluence, and favorable altcoin market conditions`;
            } else {
                reasoning = `Bullish trend confirmed by momentum (${momentum.momentum.toLowerCase()}) and ${Math.round(confluence)}% signal confluence`;
            }
        }
        // STRONG SELL: Perfect bearish alignment with extreme overbought
        else if (trend.direction === 'BEARISH' &&
                 momentum.momentum === 'STRONG_SELL' &&
                 volume.trend === 'DISTRIBUTION' &&
                 confluence >= 80 &&
                 momentum.extremeConditions >= 2) {
            recommendation = 'STRONG SELL';
            confidence = Math.min(95, confluence + 10);
            reasoning = `Strong bearish setup: ${momentum.extremeConditions} extreme overbought indicators with trend and volume confirmation`;
        }
        // SELL: Strong bearish alignment
        else if (trend.direction === 'BEARISH' &&
                 ['SELL', 'STRONG_SELL'].includes(momentum.momentum) &&
                 confluence >= 65) {
            recommendation = 'SELL';
            confidence = confluence;
            reasoning = `Bearish trend confirmed by momentum (${momentum.momentum.toLowerCase()}) and ${Math.round(confluence)}% signal confluence`;
        }
        // WEAK BUY: Moderate bullish signals
        else if ((trend.direction === 'BULLISH' || momentum.momentum === 'BUY') &&
                 confluence >= 45) {
            recommendation = 'WEAK BUY';
            confidence = confluence;
            reasoning = `Moderate bullish signals with ${Math.round(confluence)}% confluence - consider small position`;
        }
        // WEAK SELL: Moderate bearish signals
        else if ((trend.direction === 'BEARISH' || momentum.momentum === 'SELL') &&
                 confluence >= 45) {
            recommendation = 'WEAK SELL';
            confidence = confluence;
            reasoning = `Moderate bearish signals with ${Math.round(confluence)}% confluence - consider reducing position`;
        }
        // HOLD: Conflicting or weak signals
        else {
            recommendation = 'HOLD';
            confidence = Math.max(0, confluence);
            if (confluence < 30) {
                reasoning = 'Conflicting signals across indicators - wait for clearer direction';
            } else {
                reasoning = `Mixed signals with ${Math.round(confluence)}% confluence - maintain current position`;
            }
        }

        // Risk adjustment
        if (riskLevel === 'HIGH' && confidence > 0) {
            confidence = Math.max(25, confidence - 25);
            reasoning += ` (${volatility.level.toLowerCase()} volatility environment)`;
        } else if (riskLevel === 'MEDIUM' && confidence > 0) {
            confidence = Math.max(30, confidence - 15);
            reasoning += ` (${volatility.level.toLowerCase()} volatility)`;
        }

        return {
            recommendation,
            confidence: Math.round(confidence),
            reasoning,
            riskLevel,
            signals: {
                trend: `${trend.direction} (${Math.round(trend.strength)}% strength)`,
                momentum: `${momentum.momentum} (${Math.round(momentum.strength)}% strength)`,
                volume: `${volume.trend} (${Math.round(volume.strength)}% strength)`,
                volatility: volatility.level
            }
        };
    }
}

import {Injectable, Logger} from '@nestjs/common';
import {Cron, CronExpression} from '@nestjs/schedule';
import {InjectModel} from '@nestjs/mongoose';
import {Model} from 'mongoose';
import axios from 'axios';

// Schema for storing global altcoin market data
export interface AltcoinMarketData {
    btcDominance: number | null;
    altcoinSeasonIndex: number | null;
    fearGreedIndex: number | null;
    marketCapRatio: number | null;
    lastUpdate: Date;
    signals: {
        btcDominanceSignal: 'BUY' | 'SELL' | 'NEUTRAL';
        altcoinSeasonSignal: 'BUY' | 'SELL' | 'NEUTRAL';
        fearGreedSignal: 'BUY' | 'SELL' | 'NEUTRAL';
        marketCapRatioSignal: 'BUY' | 'SELL' | 'NEUTRAL';
    };
}

@Injectable()
export class AltcoinMarketService {
    private readonly logger = new Logger(AltcoinMarketService.name);
    private currentMarketData: AltcoinMarketData | null = null;
    private previousMarketData: AltcoinMarketData | null = null;

    constructor() {
        // Initialize with default data
        this.currentMarketData = {
            btcDominance: null,
            altcoinSeasonIndex: null,
            fearGreedIndex: null,
            marketCapRatio: null,
            lastUpdate: new Date(),
            signals: {
                btcDominanceSignal: 'NEUTRAL',
                altcoinSeasonSignal: 'NEUTRAL',
                fearGreedSignal: 'NEUTRAL',
                marketCapRatioSignal: 'NEUTRAL'
            }
        };
    }

    // Scheduled job to fetch altcoin market data every 5 minutes
    @Cron('*/5 * * * *') // Every 5 minutes
    async updateAltcoinMarketData() {
        this.logger.log('🌍 Fetching global altcoin market indicators...');

        try {
            // Store previous data for trend analysis
            this.previousMarketData = this.currentMarketData ? {...this.currentMarketData} : null;

            // Fetch all indicators
            const [btcDominance, fearGreedIndex] = await Promise.allSettled([
                this.fetchBitcoinDominance(),
                this.fetchFearGreedIndex()
            ]);

            // Extract values from settled promises
            const btcDominanceValue = btcDominance.status === 'fulfilled' ? btcDominance.value : null;
            const fearGreedValue = fearGreedIndex.status === 'fulfilled' ? fearGreedIndex.value : null;

            // Calculate derived indicators
            const altcoinSeasonIndex = btcDominanceValue ? Math.max(0, Math.min(100, 100 - btcDominanceValue)) : null;
            const marketCapRatio = await this.calculateMarketCapRatio();

            // Generate signals
            const signals = {
                btcDominanceSignal: this.generateAltcoinSignal('BTC_DOMINANCE', btcDominanceValue, this.previousMarketData?.btcDominance),
                altcoinSeasonSignal: this.generateAltcoinSignal('ALTCOIN_SEASON', altcoinSeasonIndex, this.previousMarketData?.altcoinSeasonIndex),
                fearGreedSignal: this.generateAltcoinSignal('FEAR_GREED', fearGreedValue, this.previousMarketData?.fearGreedIndex),
                marketCapRatioSignal: this.generateAltcoinSignal('MARKET_CAP_RATIO', marketCapRatio, this.previousMarketData?.marketCapRatio)
            };

            // Update current market data
            this.currentMarketData = {
                btcDominance: btcDominanceValue,
                altcoinSeasonIndex,
                fearGreedIndex: fearGreedValue,
                marketCapRatio,
                lastUpdate: new Date(),
                signals
            };

            this.logger.log(`✅ Altcoin market data updated: BTC Dom: ${btcDominanceValue?.toFixed(1)}%, Alt Season: ${altcoinSeasonIndex?.toFixed(1)}%, F&G: ${fearGreedValue}, Ratio: ${marketCapRatio?.toFixed(2)}`);

        } catch (error) {
            this.logger.error(`❌ Failed to update altcoin market data: ${error.message}`);
        }
    }

    // Public method to get current market data
    getCurrentMarketData(): AltcoinMarketData | null {
        return this.currentMarketData;
    }

    // Public method to get altcoin indicators for asset analysis
    getAltcoinIndicatorsForAsset(): Array<{ indicator: string; signal: string; value: number }> {
        if (!this.currentMarketData) return [];

        const indicators: Array<{ indicator: string; signal: string; value: number }> = [];

        if (this.currentMarketData.btcDominance !== null) {
            indicators.push({
                indicator: 'BTC_DOMINANCE',
                signal: this.currentMarketData.signals.btcDominanceSignal,
                value: this.currentMarketData.btcDominance
            });
        }

        if (this.currentMarketData.altcoinSeasonIndex !== null) {
            indicators.push({
                indicator: 'ALTCOIN_SEASON',
                signal: this.currentMarketData.signals.altcoinSeasonSignal,
                value: this.currentMarketData.altcoinSeasonIndex
            });
        }

        if (this.currentMarketData.fearGreedIndex !== null) {
            indicators.push({
                indicator: 'FEAR_GREED',
                signal: this.currentMarketData.signals.fearGreedSignal,
                value: this.currentMarketData.fearGreedIndex
            });
        }

        if (this.currentMarketData.marketCapRatio !== null) {
            indicators.push({
                indicator: 'MARKET_CAP_RATIO',
                signal: this.currentMarketData.signals.marketCapRatioSignal,
                value: this.currentMarketData.marketCapRatio
            });
        }

        return indicators;
    }

    // Bitcoin Dominance Index - Critical for altcoin season detection

    // Calculate based on top 50 altcoins vs Bitcoin performance over 90 days
    // This is a simplified version - in production, you'd want more sophisticated calculation

    // Altcoin season index: 100 - dominance gives us a rough altcoin strength indicator
    // When Bitcoin dominance is low (40%), altcoin index is high (60)
    // When Bitcoin dominance is high (70%), altcoin index is low (30)
    private async fetchBitcoinDominance(): Promise<number | null> {
        try {
            const response = await axios.get('https://api.coingecko.com/api/v3/global', {
                timeout: 10000
            });
            const dominance = response.data.data.market_cap_percentage.btc;
            return dominance || null;
        } catch (error) {
            this.logger.warn(`Failed to fetch Bitcoin dominance: ${error.message}`);
            return null;
        }
    }

    private async fetchFearGreedIndex(): Promise<number | null> {
        try {
            const response = await axios.get('https://api.alternative.me/fng/', {
                timeout: 10000
            });
            const fearGreedValue = response.data.data[0].value;
            return parseInt(fearGreedValue) || null;
        } catch (error) {
            this.logger.warn(`Failed to fetch Fear & Greed index: ${error.message}`);
            return null;
        }
    }

    private async calculateMarketCapRatio(): Promise<number | null> {
        try {
            const response = await axios.get('https://api.coingecko.com/api/v3/global', {
                timeout: 10000
            });
            const totalMarketCap = response.data.data.total_market_cap.usd;
            const btcMarketCap = response.data.data.market_cap_percentage.btc * totalMarketCap / 100;
            const altcoinMarketCap = totalMarketCap - btcMarketCap;

            const ratio = altcoinMarketCap / btcMarketCap;
            return ratio || null;
        } catch (error) {
            this.logger.warn(`Failed to calculate market cap ratio: ${error.message}`);
            return null;
        }
    }

    // Signal generation logic
    private generateAltcoinSignal(
        indicator: string,
        value: number | null,
        previousValue?: number | null
    ): 'BUY' | 'SELL' | 'NEUTRAL' {
        if (value === null) return 'NEUTRAL';

        switch (indicator) {
            case 'BTC_DOMINANCE':
                if (previousValue !== undefined && previousValue !== null) {
                    if (previousValue > 50 && value <= 50) return 'BUY';
                    if (previousValue < 60 && value >= 60) return 'SELL';
                    if (value < previousValue && value < 55) return 'BUY';
                    if (value > previousValue && value > 55) return 'SELL';
                }
                if (value < 45) return 'BUY';
                if (value > 65) return 'SELL';
                if (value < 50) return 'BUY';
                if (value > 60) return 'SELL';
                return 'NEUTRAL';

            case 'ALTCOIN_SEASON':
                if (previousValue !== undefined && previousValue !== null) {
                    if (previousValue < 60 && value >= 60) return 'BUY';
                    if (previousValue > 40 && value <= 40) return 'SELL';
                    if (value > previousValue && value > 50) return 'BUY';
                    if (value < previousValue && value < 50) return 'SELL';
                }
                if (value > 70) return 'BUY';
                if (value < 30) return 'SELL';
                if (value > 55) return 'BUY';
                if (value < 45) return 'SELL';
                return 'NEUTRAL';

            case 'FEAR_GREED':
                if (previousValue !== undefined && previousValue !== null) {
                    if (previousValue <= 25 && value > 25) return 'BUY';
                    if (previousValue >= 75 && value < 75) return 'SELL';
                    if (value < 20 && value < previousValue) return 'BUY';
                    if (value > 80 && value > previousValue) return 'SELL';
                }
                if (value < 20) return 'BUY';
                if (value > 80) return 'SELL';
                if (value < 35) return 'BUY';
                if (value > 70) return 'SELL';
                return 'NEUTRAL';

            case 'MARKET_CAP_RATIO':
                if (previousValue !== undefined && previousValue !== null) {
                    if (previousValue < 1.5 && value >= 1.5) return 'BUY';
                    if (previousValue > 1.0 && value <= 1.0) return 'SELL';
                    if (value > previousValue && value > 1.2) return 'BUY';
                    if (value < previousValue && value < 1.2) return 'SELL';
                }
                if (value > 2.0) return 'BUY';
                if (value < 0.8) return 'SELL';
                if (value > 1.5) return 'BUY';
                if (value < 1.0) return 'SELL';
                return 'NEUTRAL';

            default:
                return 'NEUTRAL';
        }
    }

    // Initialize data on service startup
    async onModuleInit() {
        this.logger.log('🚀 AltcoinMarketService initialized - fetching initial data...');
        await this.updateAltcoinMarketData();
    }
}

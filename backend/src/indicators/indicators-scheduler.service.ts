import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Asset } from '../crypto/schemas/asset.schema';
import { PriceData } from '../crypto/schemas/price-data.schema';
import { IndicatorsService } from './indicators.service';
import { ExchangeFactoryService } from '../crypto/exchange-factory.service';
import { WebSocketGateway } from '../websocket/websocket.gateway';
import { AltcoinMarketService } from './altcoin-market.service';

@Injectable()
export class IndicatorsSchedulerService implements OnApplicationBootstrap {
  private readonly logger = new Logger(IndicatorsSchedulerService.name);

  constructor(
    @InjectModel(Asset.name) private assetModel: Model<Asset>,
    @InjectModel(PriceData.name) private priceDataModel: Model<PriceData>,
    private readonly indicatorsService: IndicatorsService,
    private readonly exchangeFactory: ExchangeFactoryService,
    private webSocketGateway: WebSocketGateway,
    private readonly altcoinMarketService: AltcoinMarketService,
  ) {}

  // Run updates on application startup
  async onApplicationBootstrap() {
    this.logger.log('Application started - running initial price and indicator updates...');
    // Wait a few seconds for the application to fully initialize
    setTimeout(() => {
      this.updatePricesAndIndicators();
    }, 5000);
  }

  // Run every 3 minutes to update prices and indicators for all watched assets
  @Cron("0 */3 * * * *")
  async updatePricesAndIndicators() {
    try {
      this.logger.log('Starting scheduled price data and indicator updates...');

      // Broadcast that scheduler has started
      this.webSocketGateway.broadcastSchedulerStatus('started');

      // Fetch watched assets from all exchanges
      const watchedAssets = await this.assetModel.find({ isWatched: true });

      // Group assets by exchange for efficient processing
      const assetsByExchange = watchedAssets.reduce((acc, asset) => {
        const exchange = asset.exchange || 'okx'; // Default to okx for backward compatibility
        if (!acc[exchange]) {
          acc[exchange] = [];
        }
        acc[exchange].push(asset);
        return acc;
      }, {} as Record<string, any[]>);

      // Supported intervals for indicator calculations
      const intervals = ['15m', '1h', '4h', '1d'];

      let totalAssetsProcessed = 0;

      // Process each exchange separately
      for (const [exchange, assets] of Object.entries(assetsByExchange)) {
        this.logger.log(`Processing ${assets.length} assets for ${exchange.toUpperCase()} exchange`);

        // Process assets sequentially to respect rate limits
        for (const asset of assets) {
          for (const interval of intervals) {
            try {
              // First fetch and store fresh price data using the appropriate exchange service
              await this.updateAssetPriceData(asset.symbol, interval, exchange);

              // Then update indicators using the fresh data
              await this.updateAssetIndicators(asset.symbol, interval, exchange);
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              this.logger.warn(`❌ Failed to update ${asset.symbol} ${interval} on ${exchange}: ${errorMessage}`);

              // Continue with next interval even if this one fails
              continue;
            }
          }
          totalAssetsProcessed++;

          // Small delay between assets to further reduce API pressure
          await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay
        }
      }

      this.logger.log(
        `Updated prices and indicators for ${totalAssetsProcessed} assets across ${intervals.length} intervals on ${Object.keys(assetsByExchange).length} exchanges`,
      );

      // Broadcast that scheduler has completed
      this.webSocketGateway.broadcastSchedulerStatus('completed', {
        assetsProcessed: totalAssetsProcessed,
        exchanges: Object.keys(assetsByExchange).length,
        intervals: intervals.length,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`Error in scheduled price and indicator update: ${errorMessage}`);

      // Broadcast error status
      this.webSocketGateway.broadcastSchedulerStatus('completed', {
        error: errorMessage,
      });
    }
  }

  // Helper method to update price data for a specific asset and interval
  private async updateAssetPriceData(symbol: string, interval: string, exchange: string): Promise<void> {
    try {
      // Get the appropriate exchange service based on the asset's exchange
      const exchangeService = await this.getExchangeServiceForExchange(exchange);
      const priceData = await exchangeService.getFreshPriceData(symbol, interval, 100);

      if (priceData.length > 0) {
        // Replace existing price data with latest 100 candles
        await exchangeService.replacePriceData(symbol, interval, priceData);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.warn(`Failed to update price data for ${symbol} ${interval} on ${exchange}: ${errorMessage}`);
    }
  }

  private async updateAssetIndicators(symbol: string, interval: string, exchange: string) {
    try {
      // Get current asset data to access previous indicator values
      const currentAsset = await this.assetModel.findOne({ symbol, exchange }).exec();
      const previousIndicatorValues = currentAsset?.indicators?.[interval] || {};

      // Extract previous values for trend analysis (handle both old and new format)
      const getPreviousValue = (indicator: string) => {
        const prev = previousIndicatorValues[indicator];
        return prev ? (typeof prev === 'object' ? prev.value : prev) : undefined;
      };

      // Use the fresh price data we just stored in the database
      const priceData = await this.priceDataModel
        .find({ symbol, interval })
        .sort({ timestamp: -1 })
        .limit(100)
        .exec();

      if (priceData.length > 0) {
        // Reverse to get chronological order (oldest first)
        priceData.reverse();
      }

      if (priceData.length < 34) {
        this.logger.warn(
          `Not enough data for ${symbol} to calculate indicators`,
        );
        return;
      }

      const closes = priceData.map((d) => d.close);

      // Calculate professional crypto indicators
      const rsi = this.indicatorsService.calculateRSI(closes, 14);
      const macd = this.indicatorsService.calculateMACD(closes);
      const bb = this.indicatorsService.calculateBollingerBands(closes, 20);
      const stoch = this.indicatorsService.calculateStochastic(priceData, 14);
      const ao = this.indicatorsService.calculateAO(priceData);
      const obv = this.indicatorsService.calculateOBV(priceData);
      const ema12 = this.indicatorsService.calculateEMA(closes, 12);
      const ema26 = this.indicatorsService.calculateEMA(closes, 26);

      // Calculate new crypto-specific indicators
      const williamsR = this.indicatorsService.calculateWilliamsR(priceData, 14);
      const cci = this.indicatorsService.calculateCCI(priceData, 20);
      const adx = this.indicatorsService.calculateADX(priceData, 14);
      const mfi = this.indicatorsService.calculateMFI(priceData, 14);
      const vwap = this.indicatorsService.calculateVWAP(priceData);

      // Get latest values
      const latestRSI = rsi[rsi.length - 1];
      const latestMACD =
        macd.length > 0
          ? (macd[macd.length - 1] as { MACD: number }).MACD
          : null;
      const latestBB =
        bb.length > 0 ? (bb[bb.length - 1] as { middle: number }).middle : null;
      const latestBBUpper =
        bb.length > 0 ? (bb[bb.length - 1] as { upper: number }).upper : null;
      const latestBBLower =
        bb.length > 0 ? (bb[bb.length - 1] as { lower: number }).lower : null;
      const latestSTOCH =
        stoch.length > 0 ? (stoch[stoch.length - 1] as { k: number }).k : null;
      const latestAO = ao[ao.length - 1];
      const latestOBV = obv[obv.length - 1];
      const latestEMA12 = ema12[ema12.length - 1];
      const latestEMA26 = ema26[ema26.length - 1];

      // Get latest values for new indicators
      const latestWilliamsR = williamsR[williamsR.length - 1];
      const latestCCI = cci[cci.length - 1];
      const latestADX = adx[adx.length - 1];
      const latestMFI = mfi[mfi.length - 1];
      const latestVWAP = vwap[vwap.length - 1];

      // Calculate recommendation based on indicators with previous values for trend analysis
      const latestPrice = closes[closes.length - 1];

      // Calculate signals with previous values
      const rsiSignal = latestRSI !== null ? this.indicatorsService.generateSignal('RSI', latestRSI, getPreviousValue('RSI')) : null;
      const macdSignal = latestMACD !== null ? this.indicatorsService.generateSignal('MACD', latestMACD, getPreviousValue('MACD')) : null;
      const bbSignal = latestBB !== null ? this.indicatorsService.generateSignal('BB', latestBB, getPreviousValue('BB'), {
        currentPrice: latestPrice,
        upper: latestBBUpper,
        lower: latestBBLower
      }) : null;
      const stochSignal = latestSTOCH !== null ? this.indicatorsService.generateSignal('STOCH', latestSTOCH, getPreviousValue('STOCH')) : null;
      const aoSignal = latestAO !== null ? this.indicatorsService.generateSignal('AO', latestAO, getPreviousValue('AO')) : null;
      const obvSignal = latestOBV !== null ? this.indicatorsService.generateSignal('OBV', latestOBV, getPreviousValue('OBV')) : null;
      const ema12Signal = latestEMA12 !== null ? this.indicatorsService.generateSignal('EMA_12', latestEMA12, getPreviousValue('EMA_12'), { currentPrice: latestPrice }) : null;
      const ema26Signal = latestEMA26 !== null ? this.indicatorsService.generateSignal('EMA_26', latestEMA26, getPreviousValue('EMA_26'), { currentPrice: latestPrice }) : null;

      // Calculate signals for new indicators
      const williamsRSignal = latestWilliamsR !== null ? this.indicatorsService.generateSignal('WILLIAMS_R', latestWilliamsR, getPreviousValue('WILLIAMS_R')) : null;
      const cciSignal = latestCCI !== null ? this.indicatorsService.generateSignal('CCI', latestCCI, getPreviousValue('CCI')) : null;
      const adxSignal = latestADX !== null ? this.indicatorsService.generateSignal('ADX', latestADX, getPreviousValue('ADX')) : null;
      const mfiSignal = latestMFI !== null ? this.indicatorsService.generateSignal('MFI', latestMFI, getPreviousValue('MFI')) : null;
      const vwapSignal = latestVWAP !== null ? this.indicatorsService.generateSignal('VWAP', latestVWAP, getPreviousValue('VWAP'), { currentPrice: latestPrice }) : null;

      // Build indicator results with proper type safety for enhanced recommendation
      const indicatorResults: Array<{ indicator: string; signal: string; value: number }> = [];

      if (rsiSignal && latestRSI !== null) {
        indicatorResults.push({ indicator: 'RSI', value: latestRSI, signal: rsiSignal });
      }
      if (macdSignal && latestMACD !== null) {
        indicatorResults.push({ indicator: 'MACD', value: latestMACD, signal: macdSignal });
      }
      if (bbSignal && latestBB !== null) {
        indicatorResults.push({ indicator: 'BB', value: latestBB, signal: bbSignal });
      }
      if (stochSignal && latestSTOCH !== null) {
        indicatorResults.push({ indicator: 'STOCH', value: latestSTOCH, signal: stochSignal });
      }
      if (aoSignal && latestAO !== null) {
        indicatorResults.push({ indicator: 'AO', value: latestAO, signal: aoSignal });
      }
      if (obvSignal && latestOBV !== null) {
        indicatorResults.push({ indicator: 'OBV', value: latestOBV, signal: obvSignal });
      }
      if (ema12Signal && latestEMA12 !== null) {
        indicatorResults.push({ indicator: 'EMA_12', value: latestEMA12, signal: ema12Signal });
      }
      if (ema26Signal && latestEMA26 !== null) {
        indicatorResults.push({ indicator: 'EMA_26', value: latestEMA26, signal: ema26Signal });
      }
      if (williamsRSignal && latestWilliamsR !== null) {
        indicatorResults.push({ indicator: 'WILLIAMS_R', value: latestWilliamsR, signal: williamsRSignal });
      }
      if (cciSignal && latestCCI !== null) {
        indicatorResults.push({ indicator: 'CCI', value: latestCCI, signal: cciSignal });
      }
      if (adxSignal && latestADX !== null) {
        indicatorResults.push({ indicator: 'ADX', value: latestADX, signal: adxSignal });
      }
      if (mfiSignal && latestMFI !== null) {
        indicatorResults.push({ indicator: 'MFI', value: latestMFI, signal: mfiSignal });
      }
      if (vwapSignal && latestVWAP !== null) {
        indicatorResults.push({ indicator: 'VWAP', value: latestVWAP, signal: vwapSignal });
      }

      // Add altcoin-specific indicators from centralized service
      const altcoinIndicators = this.altcoinMarketService.getAltcoinIndicatorsForAsset();
      indicatorResults.push(...altcoinIndicators);

      // Use enhanced recommendation system for better trading signals
      const recommendation = this.indicatorsService.calculateEnhancedRecommendation(indicatorResults);

      // Get current price data from the latest candle
      const currentPriceData = priceData[priceData.length - 1];
      const previousPriceData = priceData[priceData.length - 2];
      const change24h = previousPriceData ? ((currentPriceData.close - previousPriceData.close) / previousPriceData.close) * 100 : 0;

      // Store current indicators with both values and signals
      const newIndicatorValues = {
        // New format: store both value and signal
        RSI: latestRSI !== null && rsiSignal ? { value: latestRSI, signal: rsiSignal } : null,
        MACD: latestMACD !== null && macdSignal ? { value: latestMACD, signal: macdSignal } : null,
        BB: latestBB !== null && bbSignal ? { value: latestBB, signal: bbSignal } : null,
        STOCH: latestSTOCH !== null && stochSignal ? { value: latestSTOCH, signal: stochSignal } : null,
        AO: latestAO !== null && aoSignal ? { value: latestAO, signal: aoSignal } : null,
        OBV: latestOBV !== null && obvSignal ? { value: latestOBV, signal: obvSignal } : null,
        EMA_12: latestEMA12 !== null && ema12Signal ? { value: latestEMA12, signal: ema12Signal } : null,
        EMA_26: latestEMA26 !== null && ema26Signal ? { value: latestEMA26, signal: ema26Signal } : null,

        // New crypto-specific indicators with signals
        WILLIAMS_R: latestWilliamsR !== null && williamsRSignal ? { value: latestWilliamsR, signal: williamsRSignal } : null,
        CCI: latestCCI !== null && cciSignal ? { value: latestCCI, signal: cciSignal } : null,
        ADX: latestADX !== null && adxSignal ? { value: latestADX, signal: adxSignal } : null,
        MFI: latestMFI !== null && mfiSignal ? { value: latestMFI, signal: mfiSignal } : null,
        VWAP: latestVWAP !== null && vwapSignal ? { value: latestVWAP, signal: vwapSignal } : null,

        // Backward compatibility: keep raw values for existing queries
        RSI_VALUE: latestRSI ?? null,
        MACD_VALUE: latestMACD ?? null,
        BB_VALUE: latestBB ?? null,
        BB_UPPER: latestBBUpper ?? null,
        BB_LOWER: latestBBLower ?? null,
        STOCH_VALUE: latestSTOCH ?? null,
        AO_VALUE: latestAO ?? null,
        OBV_VALUE: latestOBV ?? null,
        EMA_12_VALUE: latestEMA12 ?? null,
        EMA_26_VALUE: latestEMA26 ?? null,

        // New indicators raw values for backward compatibility
        WILLIAMS_R_VALUE: latestWilliamsR ?? null,
        CCI_VALUE: latestCCI ?? null,
        ADX_VALUE: latestADX ?? null,
        MFI_VALUE: latestMFI ?? null,
        VWAP_VALUE: latestVWAP ?? null,
      };

      // Update asset with latest indicator values and dashboard data (include exchange in query)
      await this.assetModel.updateOne(
        { symbol, exchange },
        {
          $set: {
            [`previousIndicators.${interval}`]: previousIndicatorValues, // Store previous values
            [`indicators.${interval}`]: newIndicatorValues, // Store new values

            [`dashboardData.${interval}`]: {
              price: currentPriceData.close,
              change24h: change24h,
              volume24h: currentPriceData.volume,
              recommendation: recommendation.recommendation,
              confidence: recommendation.confidence,
              reasoning: recommendation.reasoning || '',
              riskLevel: recommendation.riskLevel || 'MEDIUM',
              signals: recommendation.signals || {},
              lastUpdate: new Date(),
            },
            lastIndicatorUpdate: new Date(),
          },
        },
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error updating indicators for ${symbol} on ${exchange}: ${errorMessage}`,
      );
    }
  }

  // Helper method to get the appropriate exchange service based on exchange name
  private async getExchangeServiceForExchange(exchange: string) {
    // Create a temporary user settings object to force the exchange selection
    const tempUserSettings = {
      selectedExchange: exchange,
      selectedInterval: '1h',
      selectedIndicators: [],
    };

    // We need to temporarily override the exchange factory to use the specific exchange
    // For now, we'll use a direct approach based on exchange name
    switch (exchange.toLowerCase()) {
      case 'gateio':
      case 'gate.io':
        // Get Gate.io service directly from the factory
        return this.exchangeFactory['gateIOService'];
      case 'okx':
      default:
        // Get OKX service directly from the factory
        return this.exchangeFactory['cryptoService'];
    }
  }
}

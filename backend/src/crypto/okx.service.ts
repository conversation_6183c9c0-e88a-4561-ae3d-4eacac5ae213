import {Injectable, Logger} from '@nestjs/common';
import {InjectModel} from '@nestjs/mongoose';
import {Model} from 'mongoose';
import axios from 'axios';
import {Asset, AssetDocument} from './schemas/asset.schema';
import {PriceData, PriceDataDocument} from './schemas/price-data.schema';
import {UserSettingsService} from './user-settings.service';
import {AssetService} from "./asset.service";

// Rate limiter for OKX API - 20 requests per 2 seconds
class OKXRateLimiter {
    private requests: number[] = [];
    private readonly maxRequests = 20;
    private readonly windowMs = 2000; // 2 seconds

    async waitForSlot(): Promise<void> {
        const now = Date.now();

        // Remove requests older than the window
        this.requests = this.requests.filter(time => now - time < this.windowMs);

        // If we're at the limit, wait until we can make another request
        if (this.requests.length >= this.maxRequests) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = this.windowMs - (now - oldestRequest) + 100; // Add 100ms buffer

            if (waitTime > 0) {
                await new Promise(resolve => setTimeout(resolve, waitTime));
                return this.waitForSlot(); // Recursive call to check again
            }
        }

        // Record this request
        this.requests.push(now);
    }
}

@Injectable()
export class OKXService {
    private readonly logger = new Logger(OKXService.name);
    private readonly OKX_BASE_URL = 'https://www.okx.com/api/v5';
    private readonly rateLimiter = new OKXRateLimiter();

    constructor(
        @InjectModel(Asset.name) private assetModel: Model<AssetDocument>,
        @InjectModel(PriceData.name)
        private priceDataModel: Model<PriceDataDocument>,
        private userSettingsService: UserSettingsService,
        private assetService: AssetService,
    ) {
        this.logger.log('OKX API service initialized');
    }

    async getAvailableAssets(): Promise<Partial<Asset>[]> {
        try {
            // Fetch both SPOT and SWAP (perpetual) instruments
            const [spotResponse, swapResponse] = await Promise.all([
                axios.get(`${this.OKX_BASE_URL}/public/instruments`, {
                    params: {instType: 'SPOT'},
                }),
                axios.get(`${this.OKX_BASE_URL}/public/instruments`, {
                    params: {instType: 'SWAP'},
                }),
            ]);

            const assets: Partial<Asset>[] = [];

            // Add SPOT assets
            if (
                spotResponse.data &&
                (spotResponse.data as { code: string }).code === '0'
            ) {
                const responseData = spotResponse.data as {
                    data: Array<{
                        instId: string;
                        baseCcy: string;
                        quoteCcy: string;
                        state: string;
                    }>;
                };

                const spotAssets = responseData.data
                    .filter((instrument) => instrument.quoteCcy === 'USDT')
                    .map((instrument) => ({
                        symbol: instrument.instId,
                        name: `${instrument.baseCcy}/${instrument.quoteCcy} (Spot)`,
                        baseCurrency: instrument.baseCcy,
                        quoteCurrency: instrument.quoteCcy,
                        isActive: instrument.state === 'live',
                        exchange: 'okx',
                    }));
                assets.push(...spotAssets);
            }

            // Add SWAP (perpetual) assets
            if (
                swapResponse.data &&
                (swapResponse.data as { code: string }).code === '0'
            ) {
                const responseData = swapResponse.data as {
                    data: Array<{
                        instId: string;
                        ctValCcy: string;
                        settleCcy: string;
                        state: string;
                    }>;
                };

                const swapAssets = responseData.data
                    .filter((instrument) => instrument.settleCcy === 'USDT')
                    .map((instrument) => ({
                        symbol: instrument.instId,
                        name: `${instrument.ctValCcy}/USDT (Perpetual)`,
                        baseCurrency: instrument.ctValCcy,
                        quoteCurrency: 'USDT',
                        isActive: instrument.state === 'live',
                    }));
                assets.push(...swapAssets);
            }

            this.logger.log(
                `Fetched ${assets.length} available assets from OKX (SPOT + SWAP)`,
            );
            return assets;
        } catch (error) {
            this.logger.error(
                'Failed to fetch available assets from OKX',
                error instanceof Error ? error.message : String(error),
            );
            throw new Error('Failed to fetch available assets');
        }
    }

    async addAsset(symbol: string, userId: string = 'default'): Promise<Asset> {
        try {
            const existingAsset = await this.assetModel.findOne({symbol, exchange: 'okx'});
            if (existingAsset) {
                return {...existingAsset.toObject()};
            }

            // Fetch asset details from OKX
            const availableAssets = await this.getAvailableAssets();
            const assetData = availableAssets.find(
                (asset) => asset.symbol === symbol,
            );

            if (!assetData) {
                throw new Error(`Asset ${symbol} not found in OKX`);
            }

            // Get the highest position for this user's OKX assets to append new asset at the end
            const userSettings = await this.userSettingsService.getUserSettings(userId);
            const watchedSymbolCount = await this.assetService.getAssetCount(userSettings.selectedExchange,
                userSettings.selectedCategory);

            // New asset will be at the end
            // Create new asset with isWatched set to true
            const newAsset = new this.assetModel({
                ...assetData,
                exchange: 'okx',
                position: watchedSymbolCount,
                category: userSettings.selectedCategory
            });
            const savedAsset = await newAsset.save();

            // Auto-fetch price data for new asset
            void this.autoFetchPriceData(symbol);

            this.logger.log(`Added and watching asset: ${symbol}`);
            return savedAsset;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error adding asset ${symbol}:`, errorMessage);
            throw new Error(`Failed to add asset: ${errorMessage}`);
        }
    }

    private async autoFetchPriceData(symbol: string) {
        const intervals = ['15m', '1h', '4h', '1d', '1w'];

        for (const interval of intervals) {
            try {
                // Fetch fresh price data including current candle
                const priceData = await this.getFreshPriceData(symbol, interval, 100);

                if (priceData.length > 0) {
                    // Replace existing data with latest 100 candles
                    await this.replacePriceData(symbol, interval, priceData);
                    this.logger.log(`Auto-fetched ${interval} data for ${symbol}: ${priceData.length} candles`);
                }
            } catch (error) {
                const errorMessage =
                    error instanceof Error ? error.message : String(error);
                this.logger.warn(
                    `Failed to auto-fetch ${interval} data for ${symbol}:`,
                    errorMessage,
                );
            }
        }
    }

    async getWatchedAssets(userId: string = 'default'): Promise<Asset[]> {
        const userSettings = await this.userSettingsService.getUserSettings(userId);

        return this.assetModel.find({
            exchange: 'okx',
            category: userSettings.selectedCategory
        }).sort({position: 1}).exec(); // Sort by position ascending
    }

    async removeAsset(symbol: string, userId: string = 'default'): Promise<boolean> {
        try {
            const userSettings = await this.userSettingsService.getUserSettings(userId);

            // Don't delete the asset, just mark as not watched to preserve historical data
            const result = await this.assetModel.deleteOne(
                {
                    symbol, exchange: 'okx',
                    category: userSettings.selectedCategory,
                },
            );

            return true;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error removing asset ${symbol}:`, errorMessage);
            return false;
        }
    }

    // Old fetchPriceData method removed - now using getFreshPriceData + replacePriceData

    private mapIntervalToOKX(interval: string): string {
        // Map our interval format to OKX API format
        const intervalMap: { [key: string]: string } = {
            '15m': '15m',
            '1h': '1H',
            '4h': '4H',
            '1d': '1D',
        };

        return intervalMap[interval] || interval;
    }

    async getPriceData(
        symbol: string,
        interval: string,
        limit: number = 100,
    ): Promise<PriceData[]> {
        return this.priceDataModel
            .find({symbol, interval})
            .sort({timestamp: -1})
            .limit(limit)
            .exec();
    }

    // Fetch fresh price data directly from OKX API including current incomplete candle
    async getFreshPriceData(
        symbol: string,
        interval: string,
        limit: number = 100,
    ): Promise<PriceData[]> {
        try {
            const okxInterval = this.mapIntervalToOKX(interval);

            // Wait for rate limit slot before making candles request
            await this.rateLimiter.waitForSlot();

            // Get completed candles (one less to make room for current candle)
            const candlesResponse = await axios.get(`${this.OKX_BASE_URL}/market/candles`, {
                params: {
                    instId: symbol.replace("_", "-"),
                    bar: okxInterval,
                    limit: (limit - 1).toString(),
                },
            });

            if (candlesResponse.data.code !== '0') {
                throw new Error(`OKX API error: ${candlesResponse.data.msg}`);
            }

            const completedCandles = candlesResponse.data.data || [];

            // Wait for rate limit slot before making ticker request
            await this.rateLimiter.waitForSlot();

            // Convert completed candles to our format
            const completedPriceData = this.convertCandlesToPriceData(completedCandles, symbol, interval);

            // Add current candle at the beginning (newest first, as OKX returns)
            const allPriceData = [...completedPriceData];

            return allPriceData;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(
                `Error fetching fresh price data for ${symbol} ${interval}: ${errorMessage}`,
            );
            throw error;
        }
    }

    // Helper method to convert OKX candle format to PriceData
    private convertCandlesToPriceData(candles: string[][], symbol: string, interval: string): PriceData[] {
        return candles.map((candle: string[]) => ({
            symbol,
            interval,
            timestamp: new Date(parseInt(candle[0])),
            open: parseFloat(candle[1]),
            high: parseFloat(candle[2]),
            low: parseFloat(candle[3]),
            close: parseFloat(candle[4]),
            volume: parseFloat(candle[5]),
            createdAt: new Date(),
        }));
    }

    // Replace price data with latest 100 candles (overwrite, don't accumulate)
    async replacePriceData(symbol: string, interval: string, priceData: PriceData[]): Promise<void> {
        try {
            if (priceData.length === 0) return;

            // Sort by timestamp descending and keep only latest 100
            const sortedCandles = priceData
                .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
                .slice(0, 100);

            // Delete all existing data for this symbol-interval combination
            await this.priceDataModel.deleteMany({symbol, interval});

            // Insert the latest 100 candles
            await this.priceDataModel.insertMany(sortedCandles);
        } catch (error) {
            this.logger.error(`Error replacing price data for ${symbol} ${interval}:`, error);
            throw error;
        }
    }

    async getMultipleTickers(symbols: string[]): Promise<any[]> {
        try {
            await this.rateLimiter.waitForSlot();

            // Get tickers for all symbols
            const tickers: any[] = [];
            for (const symbol of symbols) {
                try {
                    const response = await axios.get(`${this.OKX_BASE_URL}/market/ticker`, {
                        params: {instId: symbol}
                    });

                    if (response.data && response.data.data && response.data.data.length > 0) {
                        const ticker = response.data.data[0];
                        tickers.push({
                            symbol: ticker.instId,
                            price: parseFloat(ticker.last),
                            change24h: parseFloat(ticker.chg) * 100, // Convert to percentage
                            volume24h: parseFloat(ticker.vol24h),
                        });
                    }
                } catch (error) {
                    this.logger.warn(`Failed to get ticker for ${symbol}:`, error);
                }
            }

            return tickers;
        } catch (error) {
            this.logger.error('Error fetching multiple tickers from OKX:', error);
            throw error;
        }
    }

    async updateAssetPositions(positions: any[], userId: string = 'default'): Promise<boolean> {
        try {
            // Update positions for assets in this exchange (OKX)
            for (const positionUpdate of positions) {
                await this.assetModel.updateOne(
                    {symbol: positionUpdate.symbol, exchange: 'okx'},
                    {$set: {position: positionUpdate.position}}
                );
            }

            this.logger.log(`Updated positions for ${positions.length} OKX assets`);
            return true;
        } catch (error) {
            this.logger.error('Error updating asset positions:', error);
            return false;
        }
    }

    async updateAssetCategory(symbol: string, category: string | null, userId: string = 'default'): Promise<boolean> {
        try {
            const result = await this.assetModel.updateOne(
                {symbol, exchange: 'okx'},
                {$set: {category: category}}
            );

            if (result.matchedCount === 0) {
                this.logger.warn(`Asset ${symbol} not found for category update`);
                return false;
            }

            this.logger.log(`Updated category for asset ${symbol} to: ${category || 'none'}`);
            return true;
        } catch (error) {
            this.logger.error('Error updating asset category:', error);
            return false;
        }
    }
}

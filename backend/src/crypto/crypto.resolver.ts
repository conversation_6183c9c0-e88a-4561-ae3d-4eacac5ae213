import {Args, Field, Float, Mutation, ObjectType, Query, Resolver, InputType} from '@nestjs/graphql';
import {OKXService} from './okx.service';
import {Asset} from './schemas/asset.schema';
import {ExchangeFactoryService} from './exchange-factory.service';

@InputType()
export class AssetPositionInput {
    @Field()
    symbol: string;

    @Field()
    position: number;
}

@ObjectType()
export class TickerData {
    @Field()
    symbol: string;

    @Field(() => Float)
    price: number;

    @Field(() => Float)
    change24h: number;

    @Field(() => Float)
    volume24h: number;
}

@Resolver(() => Asset)
export class CryptoResolver {
    constructor(
        private readonly okxService: OKXService,
        private readonly exchangeFactory: ExchangeFactoryService,
    ) {}

    @Query(() => [Asset])
    async availableAssets(): Promise<Partial<Asset>[]> {
        const exchangeService = await this.exchangeFactory.getExchangeService();
        return exchangeService.getAvailableAssets();
    }

    @Query(() => [Asset])
    async watchedAssets(
        @Args('userId', { type: () => String, defaultValue: 'default' }) userId: string,
    ): Promise<Asset[]> {
        // Use user's selected exchange from settings to get the appropriate service
        const exchangeService = await this.exchangeFactory.getExchangeService(userId);
        return exchangeService.getWatchedAssets(userId);
    }

    @Mutation(() => Asset)
    async addAsset(
        @Args('symbol') symbol: string,
        @Args('userId', { type: () => String, defaultValue: 'default' }) userId: string,
    ): Promise<Asset> {
        const exchangeService = await this.exchangeFactory.getExchangeService(userId);
        return exchangeService.addAsset(symbol, userId);
    }

    @Mutation(() => Boolean)
    async removeAsset(
        @Args('symbol') symbol: string,
        @Args('userId', { type: () => String, defaultValue: 'default' }) userId: string,
    ): Promise<boolean> {
        const exchangeService = await this.exchangeFactory.getExchangeService(userId);
        return exchangeService.removeAsset(symbol, userId);
    }

    @Query(() => [String])
    async availableExchanges(): Promise<string[]> {
        return this.exchangeFactory.getAvailableExchanges();
    }

    @Mutation(() => Boolean)
    async updateAssetPositions(
        @Args('positions', { type: () => [AssetPositionInput] }) positions: AssetPositionInput[],
        @Args('userId', { type: () => String, defaultValue: 'default' }) userId: string,
    ): Promise<boolean> {
        const exchangeService = await this.exchangeFactory.getExchangeService(userId);
        return exchangeService.updateAssetPositions(positions, userId);
    }

    @Mutation(() => Boolean)
    async updateAssetCategory(
        @Args('symbol', { type: () => String }) symbol: string,
        @Args('category', { type: () => String, nullable: true }) category: string | null,
        @Args('userId', { type: () => String, defaultValue: 'default' }) userId: string,
    ): Promise<boolean> {
        const exchangeService = await this.exchangeFactory.getExchangeService(userId);
        return exchangeService.updateAssetCategory(symbol, category, userId);
    }
}

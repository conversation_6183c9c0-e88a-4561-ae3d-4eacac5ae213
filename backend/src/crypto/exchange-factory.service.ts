import { Injectable, Logger } from '@nestjs/common';
import { OKXService } from './okx.service';
import { GateIOService } from './gateio.service';
import { UserSettingsService } from './user-settings.service';

export interface ExchangeInterface {
    getAvailableAssets(): Promise<any[]>;
    addAsset(symbol: string, userId?: string): Promise<any>;
    removeAsset(symbol: string, userId?: string): Promise<boolean>;
    getWatchedAssets(userId?: string): Promise<any[]>;
    getPriceData(symbol: string, interval: string, limit?: number): Promise<any[]>;
    getFreshPriceData(symbol: string, interval: string, limit?: number): Promise<any[]>;
    getMultipleTickers(symbols: string[]): Promise<any[]>;
    replacePriceData(symbol: string, interval: string, priceData: any[]): Promise<void>;
    updateAssetPositions(positions: any[], userId?: string): Promise<boolean>;
    updateAssetCategory(symbol: string, category: string | null, userId?: string): Promise<boolean>;
}

@Injectable()
export class ExchangeFactoryService {
    private readonly logger = new Logger(ExchangeFactoryService.name);

    constructor(
        private readonly okxService: OKXService,
        private readonly gateIOService: GateIOService,
        private readonly userSettingsService: UserSettingsService,
    ) {}

    async getExchangeService(userId: string = 'default'): Promise<ExchangeInterface> {
        try {
            const userSettings = await this.userSettingsService.getUserSettings(userId);
            const selectedExchange = userSettings.selectedExchange || 'okx';

            switch (selectedExchange.toLowerCase()) {
                case 'gateio':
                case 'gate.io':
                    this.logger.log('Using Gate.io exchange service');
                    return this.gateIOService;
                case 'okx':
                default:
                    this.logger.log('Using OKX exchange service');
                    return this.okxService;
            }
        } catch (error) {
            this.logger.warn('Failed to get user exchange preference, defaulting to OKX:', error);
            return this.okxService;
        }
    }

    async getAvailableExchanges(): Promise<string[]> {
        return ['okx', 'gateio'];
    }

    getExchangeDisplayName(exchange: string): string {
        const displayNames: { [key: string]: string } = {
            'okx': 'OKX',
            'gateio': 'Gate.io',
        };
        return displayNames[exchange.toLowerCase()] || exchange;
    }
}

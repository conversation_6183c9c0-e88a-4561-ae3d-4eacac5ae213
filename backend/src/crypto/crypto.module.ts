import {Modu<PERSON>} from '@nestjs/common';
import {MongooseModule} from '@nestjs/mongoose';
import {OKXService} from './okx.service';
import {GateIOService} from './gateio.service';
import {ExchangeFactoryService} from './exchange-factory.service';
import {CryptoResolver} from './crypto.resolver';
import {UserSettingsService} from './user-settings.service';
import {UserSettingsResolver} from './user-settings.resolver';
import {Asset, AssetSchema} from './schemas/asset.schema';
import {PriceData, PriceDataSchema} from './schemas/price-data.schema';
import {
    UserSettings,
    UserSettingsSchema,
} from './schemas/user-settings.schema';
import {AssetService} from "./asset.service";

@Module({
    imports: [
        MongooseModule.forFeature([
            {name: Asset.name, schema: AssetSchema},
            {name: PriceData.name, schema: PriceDataSchema},
            {name: UserSettings.name, schema: UserSettingsSchema},
        ]),
    ],
    providers: [
        OKXService,
        GateIOService,
        ExchangeFactoryService,
        CryptoResolver,
        UserSettingsService,
        UserSettingsResolver,
        AssetService
    ],
    exports: [OKXService, GateIOService, ExchangeFactoryService, UserSettingsService, AssetService],
})
export class CryptoModule {
}

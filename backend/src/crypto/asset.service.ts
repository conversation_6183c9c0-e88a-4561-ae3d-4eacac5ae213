import {Injectable} from '@nestjs/common';
import {InjectModel} from '@nestjs/mongoose';
import {Model} from 'mongoose';
import {Asset} from "./schemas/asset.schema";

@Injectable()
export class AssetService {
    constructor(
        @InjectModel(Asset.name)
        private assetModel: Model<Asset>,
    ) {
    }

    async getAssetCount(exchange: string, category: string): Promise<number> {
        const query = {
            category,
            exchange
        }
        return this.assetModel.countDocuments(query);
    }
}

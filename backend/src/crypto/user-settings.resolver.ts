import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { UserSettingsService } from './user-settings.service';
import { UserSettings } from './schemas/user-settings.schema';

@Resolver(() => UserSettings)
export class UserSettingsResolver {
  constructor(private readonly userSettingsService: UserSettingsService) {}

  @Query(() => UserSettings)
  async getUserSettings(
    @Args('userId', { type: () => String, defaultValue: 'default' })
    userId: string,
  ): Promise<UserSettings> {
    return this.userSettingsService.getUserSettings(userId);
  }

  @Query(() => [String])
  async availableCategories(): Promise<string[]> {
    return ['all', 'grayscale', 'favorites'];
  }

  @Mutation(() => UserSettings)
  async updateInterval(
    @Args('interval', { type: () => String }) interval: string,
    @Args('userId', { type: () => String, defaultValue: 'default' })
    userId: string,
  ): Promise<UserSettings> {
    return this.userSettingsService.updateInterval(userId, interval);
  }

  @Mutation(() => UserSettings)
  async updateIndicators(
    @Args('indicators', { type: () => [String] }) indicators: string[],
    @Args('userId', { type: () => String, defaultValue: 'default' })
    userId: string,
  ): Promise<UserSettings> {
    return this.userSettingsService.updateIndicators(userId, indicators);
  }

  @Mutation(() => UserSettings)
  async updateExchange(
    @Args('exchange', { type: () => String }) exchange: string,
    @Args('userId', { type: () => String, defaultValue: 'default' })
    userId: string,
  ): Promise<UserSettings> {
    return this.userSettingsService.updateExchange(userId, exchange);
  }

  @Mutation(() => UserSettings)
  async updateSelectedCategory(
    @Args('category', { type: () => String }) category: string,
    @Args('userId', { type: () => String, defaultValue: 'default' })
    userId: string,
  ): Promise<UserSettings> {
    return this.userSettingsService.updateSelectedCategory(userId, category);
  }
}

import {Prop, Schema, SchemaFactory} from '@nestjs/mongoose';
import {Document} from 'mongoose';
import {ObjectType, Field, ID} from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

@Schema({timestamps: true})
@ObjectType()
export class UserSettings extends Document {
    @Field(() => ID)
    declare _id: string;

    @Field()
    @Prop({required: true, default: 'default'})
    userId: string;

    @Field()
    @Prop({required: true, default: '1h'})
    selectedInterval: string;

    @Field(() => [String])
    @Prop({type: [String], default: ['EMA_12', 'RSI', 'AO']})
    selectedIndicators: string[];

    @Field()
    @Prop({required: true, default: 'okx'})
    selectedExchange: string;

    @Field()
    @Prop({required: true, default: 'all'})
    selectedCategory: string;

    @Field()
    @Prop({default: Date.now})
    createdAt: Date;

    @Field()
    @Prop({default: Date.now})
    updatedAt: Date;
}

export const UserSettingsSchema = SchemaFactory.createForClass(UserSettings);

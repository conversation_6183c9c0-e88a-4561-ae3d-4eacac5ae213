import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectType, Field, Float } from '@nestjs/graphql';

export type PriceDataDocument = PriceData & Document;

@ObjectType()
@Schema({ timestamps: true })
export class PriceData {
  @Field()
  @Prop({ required: true })
  symbol: string;

  @Field()
  @Prop({ required: true })
  interval: string; // '15m', '1h', '1d', etc.

  @Field(() => Float)
  @Prop({ required: true })
  open: number;

  @Field(() => Float)
  @Prop({ required: true })
  high: number;

  @Field(() => Float)
  @Prop({ required: true })
  low: number;

  @Field(() => Float)
  @Prop({ required: true })
  close: number;

  @Field(() => Float)
  @Prop({ required: true })
  volume: number;

  @Field()
  @Prop({ required: true })
  timestamp: Date;

  @Field()
  @Prop({ default: Date.now })
  createdAt: Date;
}

export const PriceDataSchema = SchemaFactory.createForClass(PriceData);

// Create compound index for efficient queries
PriceDataSchema.index({ symbol: 1, interval: 1, timestamp: 1 });

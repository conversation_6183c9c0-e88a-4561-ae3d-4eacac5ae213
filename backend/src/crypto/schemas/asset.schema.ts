import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

export type AssetDocument = Asset & Document;

@ObjectType()
@Schema({ timestamps: true })
export class Asset {
  @Field(() => ID)
  _id: string;

  @Field()
  @Prop({ required: true })
  symbol: string;

  @Field()
  @Prop({ required: true })
  name: string;

  @Field()
  @Prop({ required: true })
  baseCurrency: string;

  @Field()
  @Prop({ required: true })
  quoteCurrency: string;

  @Field({ nullable: true })
  @Prop()
  logoUrl?: string;

  @Field()
  @Prop({ default: true })
  isActive: boolean;

  @Field()
  @Prop({ required: true, default: 'okx' })
  exchange: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({
    type: Object, // Use flexible Object type to handle both old and new formats
    default: {},
  })
  indicators: Record<string, any>; // Flexible type to handle both old and new formats
  // Supports: RSI, MACD, BB, STOCH, AO, OBV, EMA_12, EMA_26, WILLIAMS_R, CCI, ADX, MFI, VWAP

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({
    type: Object,
    default: {},
  })
  previousIndicators: Record<string, any>; // Flexible type to handle both old and new formats

  @Field({ nullable: true })
  @Prop({ type: Date })
  lastIndicatorUpdate: Date;



  // Pre-calculated dashboard data for each interval
  @Field(() => String, { nullable: true })
  @Prop({
    type: {
      '15m': {
        price: { type: Number, default: null },
        change24h: { type: Number, default: null },
        volume24h: { type: Number, default: null },
        recommendation: { type: String, default: null },
        confidence: { type: Number, default: null },
        lastUpdate: { type: Date, default: null },
      },
      '1h': {
        price: { type: Number, default: null },
        change24h: { type: Number, default: null },
        volume24h: { type: Number, default: null },
        recommendation: { type: String, default: null },
        confidence: { type: Number, default: null },
        lastUpdate: { type: Date, default: null },
      },
      '4h': {
        price: { type: Number, default: null },
        change24h: { type: Number, default: null },
        volume24h: { type: Number, default: null },
        recommendation: { type: String, default: null },
        confidence: { type: Number, default: null },
        lastUpdate: { type: Date, default: null },
      },
      '1d': {
        price: { type: Number, default: null },
        change24h: { type: Number, default: null },
        volume24h: { type: Number, default: null },
        recommendation: { type: String, default: null },
        confidence: { type: Number, default: null },
        lastUpdate: { type: Date, default: null },
      },
    },
    default: {},
  })
  dashboardData: Record<string, {
    price?: number;
    change24h?: number;
    volume24h?: number;
    recommendation?: string;
    confidence?: number;
    reasoning?: string;
    riskLevel?: string;
    signals?: any;
    altcoinIndicators?: {
      BTC_DOMINANCE?: number;
      ALTCOIN_SEASON?: number;
      FEAR_GREED?: number;
      MARKET_CAP_RATIO?: number;
    };
    lastUpdate?: Date;
  }>;

  @Field()
  @Prop({ default: 0 })
  position: number;

  @Field({ nullable: true })
  @Prop({ default: null })
  category: string;

  @Field()
  @Prop({ default: Date.now })
  createdAt: Date;

  @Field()
  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const AssetSchema = SchemaFactory.createForClass(Asset);

// Create compound index for symbol + exchange
AssetSchema.index({ symbol: 1, exchange: 1 }, { unique: true });

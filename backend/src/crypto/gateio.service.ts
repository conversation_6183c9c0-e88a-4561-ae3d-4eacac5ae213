import {Injectable, Logger} from '@nestjs/common';
import {InjectModel} from '@nestjs/mongoose';
import {Model} from 'mongoose';
import {Asset, AssetDocument} from './schemas/asset.schema';
import {PriceData, PriceDataDocument} from './schemas/price-data.schema';
import {UserSettingsService} from './user-settings.service';
import {AssetService} from "./asset.service";

// Import Gate.io API
const {ApiClient, SpotApi} = require('gate-api');

// Rate limiter for Gate.io API - 900 requests per minute
class GateIORateLimiter {
    private requests: number[] = [];
    private readonly maxRequests = 900;
    private readonly windowMs = 60000; // 1 minute

    async waitForSlot(): Promise<void> {
        const now = Date.now();

        // Remove requests older than the window
        this.requests = this.requests.filter(time => now - time < this.windowMs);

        // If we're at the limit, wait until we can make another request
        if (this.requests.length >= this.maxRequests) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = this.windowMs - (now - oldestRequest) + 100; // Add 100ms buffer

            if (waitTime > 0) {
                await new Promise(resolve => setTimeout(resolve, waitTime));
                return this.waitForSlot(); // Recursive call to check again
            }
        }

        // Record this request
        this.requests.push(now);
    }
}

@Injectable()
export class GateIOService {
    private readonly logger = new Logger(GateIOService.name);
    private readonly rateLimiter = new GateIORateLimiter();
    private readonly client: any;
    private readonly spotApi: any;

    constructor(
        @InjectModel(Asset.name) private assetModel: Model<AssetDocument>,
        @InjectModel(PriceData.name) private priceDataModel: Model<PriceDataDocument>,
        private userSettingsService: UserSettingsService,
        private assetService: AssetService,
    ) {
        // Initialize Gate.io API client
        this.client = new ApiClient();
        this.spotApi = new SpotApi(this.client);
        this.logger.log('Gate.io API service initialized');
    }

    async getAvailableAssets(): Promise<Partial<Asset>[]> {
        try {
            await this.rateLimiter.waitForSlot();

            // Get all currency pairs
            const pairs = await this.spotApi.listCurrencyPairs();

            const assets: Partial<Asset>[] = [];

            if (pairs && pairs.body) {
                const usdtPairs = pairs.body
                    .filter((pair: any) => pair.quote === 'USDT' && pair.tradeStatus === 'tradable')
                    .map((pair: any) => ({
                        symbol: pair.id,
                        name: `${pair.base}/${pair.quote}`,
                        baseCurrency: pair.base,
                        quoteCurrency: pair.quote,
                        isActive: pair.trade_status === 'tradable',
                        exchange: 'gateio',
                    }));

                assets.push(...usdtPairs);
            }

            this.logger.log(`Fetched ${assets.length} available assets from Gate.io`);
            return assets;
        } catch (error) {
            this.logger.error(
                'Failed to fetch available assets from Gate.io',
                error instanceof Error ? error.message : String(error),
            );
            throw new Error('Failed to fetch available assets from Gate.io');
        }
    }

    async addAsset(symbol: string, userId: string = 'default'): Promise<Asset> {
        try {
            const existingAsset = await this.assetModel.findOne({symbol, exchange: 'gateio'});
            if (existingAsset) {
                return {...existingAsset.toObject()};
            }

            // Fetch asset details from Gate.io
            const availableAssets = await this.getAvailableAssets();
            const assetData = availableAssets.find(
                (asset) => asset.symbol === symbol,
            );

            if (!assetData) {
                throw new Error(`Asset ${symbol} not found in Gate.io`);
            }

            // Get the highest position for this user's Gate.io assets to append new asset at the end
            const userSettings = await this.userSettingsService.getUserSettings(userId);
            const watchedSymbolCount = await this.assetService.getAssetCount(userSettings.selectedExchange,
                userSettings.selectedCategory);

            // Create new asset with isWatched set to true
            const newAsset = new this.assetModel({
                ...assetData,
                isWatched: true,
                exchange: 'gateio',
                position: watchedSymbolCount,
            });
            const savedAsset = await newAsset.save();

            // Auto-fetch price data for new asset
            void this.autoFetchPriceData(symbol);

            this.logger.log(`Added and watching asset: ${symbol}`);
            return savedAsset;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(`Error adding asset ${symbol}:`, errorMessage);
            throw new Error(`Failed to add asset: ${errorMessage}`);
        }
    }

    private async autoFetchPriceData(symbol: string) {
        const intervals = ['15m', '1h', '4h', '1d'];

        for (const interval of intervals) {
            try {
                // Fetch fresh price data including current candle
                const priceData = await this.getFreshPriceData(symbol, interval, 100);

                if (priceData.length > 0) {
                    // Replace existing data with latest 100 candles
                    await this.replacePriceData(symbol, interval, priceData);
                    this.logger.log(`Auto-fetched ${interval} data for ${symbol}: ${priceData.length} candles`);
                }
            } catch (error) {
                const errorMessage =
                    error instanceof Error ? error.message : String(error);
                this.logger.warn(
                    `Failed to auto-fetch ${interval} data for ${symbol}:`,
                    errorMessage,
                );
            }
        }
    }

    private mapIntervalToGateIO(interval: string): string {
        // Map our interval format to Gate.io API format
        const intervalMap: { [key: string]: string } = {
            '15m': '15m',
            '1h': '1h',
            '4h': '4h',
            '1d': '1d',
        };

        return intervalMap[interval] || interval;
    }

    async getPriceData(
        symbol: string,
        interval: string,
        limit: number = 100,
    ): Promise<PriceData[]> {
        return this.priceDataModel
            .find({symbol, interval})
            .sort({timestamp: -1})
            .limit(limit)
            .exec();
    }

    // Fetch fresh price data directly from Gate.io API
    async getFreshPriceData(
        symbol: string,
        interval: string,
        limit: number = 100,
    ): Promise<PriceData[]> {
        try {
            const gateInterval = this.mapIntervalToGateIO(interval);

            await this.rateLimiter.waitForSlot();

            // Get candlestick data
            const candlesResponse = await this.spotApi.listCandlesticks(symbol
                .replace("-", "_")
                .replace("-SWAP", ""), {
                interval: gateInterval,
                limit: limit,
            });

            if (!candlesResponse || !candlesResponse.body) {
                throw new Error('No candlestick data received from Gate.io');
            }

            const candles = candlesResponse.body;

            // Convert Gate.io candles to our format
            const priceData = this.convertCandlesToPriceData(candles, symbol, interval);

            return priceData;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            this.logger.error(
                `Error fetching fresh price data for ${symbol} ${interval}: ${errorMessage}`,
            );
            throw error;
        }
    }

    // Helper method to convert Gate.io candle format to PriceData
    private convertCandlesToPriceData(candles: any[], symbol: string, interval: string): PriceData[] {
        return candles.map((candle: any) => ({
            symbol,
            interval,
            timestamp: new Date(parseInt(candle[0]) * 1000), // Gate.io uses seconds, we need milliseconds
            volume: parseFloat(candle[1]),
            close: parseFloat(candle[2]),
            high: parseFloat(candle[3]),
            low: parseFloat(candle[4]),
            open: parseFloat(candle[5]),
            createdAt: new Date(),
        }));
    }

    // Replace price data with latest 100 candles (overwrite, don't accumulate)
    async replacePriceData(symbol: string, interval: string, priceData: PriceData[]): Promise<void> {
        try {
            if (priceData.length === 0) return;

            // Sort by timestamp descending and keep only latest 100
            const sortedCandles = priceData
                .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
                .slice(0, 100);

            // Delete all existing data for this symbol-interval combination
            await this.priceDataModel.deleteMany({symbol, interval});

            // Insert the latest 100 candles
            await this.priceDataModel.insertMany(sortedCandles);
        } catch (error) {
            this.logger.error(`Error replacing price data for ${symbol} ${interval}:`, error);
            throw error;
        }
    }

    async getMultipleTickers(symbols: string[]): Promise<any[]> {
        try {
            await this.rateLimiter.waitForSlot();

            // Get all tickers
            const tickersResponse = await this.spotApi.listTickers();

            if (!tickersResponse || !tickersResponse.body) {
                throw new Error('No ticker data received from Gate.io');
            }

            const allTickers = tickersResponse.body;

            // Filter for requested symbols
            const requestedTickers = allTickers
                .filter((ticker: any) => symbols.includes(ticker.currency_pair))
                .map((ticker: any) => ({
                    symbol: ticker.currency_pair,
                    price: parseFloat(ticker.last),
                    change24h: parseFloat(ticker.change_percentage),
                    volume24h: parseFloat(ticker.base_volume),
                }));

            return requestedTickers;
        } catch (error) {
            this.logger.error('Error fetching multiple tickers from Gate.io:', error);
            throw error;
        }
    }

    async getWatchedAssets(userId: string = 'default'): Promise<Asset[]> {
        const userSettings = await this.userSettingsService.getUserSettings(userId);

        return this.assetModel.find({
            exchange: 'gateio',
            category: userSettings.selectedCategory
        }).sort({position: 1}).exec(); // Sort by position ascending
    }

    async removeAsset(symbol: string, userId: string = 'default'): Promise<boolean> {
        try {
            const userSettings = await this.userSettingsService.getUserSettings(userId);
            await this.assetModel.deleteOne(
                {symbol, exchange: 'gateio', category: userSettings.selectedCategory},
            );

            return true;
        } catch (error) {
            this.logger.error(`Error removing asset ${symbol}:`, error);
            return false;
        }
    }

    async updateAssetPositions(positions: any[], userId: string = 'default'): Promise<boolean> {
        try {
            // Update positions for assets in this exchange (Gate.io)
            for (const positionUpdate of positions) {
                await this.assetModel.updateOne(
                    {symbol: positionUpdate.symbol, exchange: 'gateio'},
                    {$set: {position: positionUpdate.position}}
                );
            }

            this.logger.log(`Updated positions for ${positions.length} Gate.io assets`);
            return true;
        } catch (error) {
            this.logger.error('Error updating asset positions:', error);
            return false;
        }
    }

    async updateAssetCategory(symbol: string, category: string | null, userId: string = 'default'): Promise<boolean> {
        try {
            const result = await this.assetModel.updateOne(
                {symbol, exchange: 'gateio'},
                {$set: {category: category}}
            );

            if (result.matchedCount === 0) {
                this.logger.warn(`Asset ${symbol} not found for category update`);
                return false;
            }

            this.logger.log(`Updated category for asset ${symbol} to: ${category || 'none'}`);
            return true;
        } catch (error) {
            this.logger.error('Error updating asset category:', error);
            return false;
        }
    }
}

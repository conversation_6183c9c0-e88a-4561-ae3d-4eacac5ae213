import {Injectable} from '@nestjs/common';
import {InjectModel} from '@nestjs/mongoose';
import {Model} from 'mongoose';
import {UserSettings} from './schemas/user-settings.schema';

@Injectable()
export class UserSettingsService {
    constructor(
        @InjectModel(UserSettings.name)
        private userSettingsModel: Model<UserSettings>,
    ) {
    }

    async getUserSettings(userId: string = 'default'): Promise<UserSettings> {
        let settings = await this.userSettingsModel.findOne({userId});

        if (!settings) {
            settings = new this.userSettingsModel({
                userId,
                selectedInterval: '1h',
                selectedIndicators: ['EMA_12', 'RSI', 'AO'],
                selectedExchange: 'okx',
                selectedCategory: 'all',
            });
            await settings.save();
        }

        return settings;
    }

    async updateInterval(
        userId: string = 'default',
        interval: string,
    ): Promise<UserSettings> {
        return this.userSettingsModel.findOneAndUpdate(
            {userId},
            {selectedInterval: interval, updatedAt: new Date()},
            {new: true, upsert: true},
        );
    }

    async updateIndicators(
        userId: string = 'default',
        indicators: string[],
    ): Promise<UserSettings> {
        return this.userSettingsModel.findOneAndUpdate(
            {userId},
            {selectedIndicators: indicators, updatedAt: new Date()},
            {new: true, upsert: true},
        );
    }

    async updateExchange(
        userId: string = 'default',
        exchange: string,
    ): Promise<UserSettings> {
        return this.userSettingsModel.findOneAndUpdate(
            {userId},
            {selectedExchange: exchange, updatedAt: new Date()},
            {new: true, upsert: true},
        );
    }

    async updateSelectedCategory(
        userId: string = 'default',
        category: string,
    ): Promise<UserSettings> {
        return this.userSettingsModel.findOneAndUpdate(
            {userId},
            {selectedCategory: category, updatedAt: new Date()},
            {new: true, upsert: true},
        );
    }
}

import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    reactStrictMode: false, // Disabled to fix issues with react-beautiful-dnd
    swcMinify: true,
    images: {
        domains: ['localhost', 'monitor.dsserv.de'],
        unoptimized: true, // Required for static export
    },
    output: 'export', // Changed from 'standalone' to 'export' for Tauri
    trailingSlash: true,
    distDir: 'out', // Ensure output goes to 'out' directory
};

export default nextConfig;

import {gql} from '@apollo/client';

export const ADD_ASSET = gql`
    mutation AddAsset($symbol: String!) {
        addAsset(symbol: $symbol) {
            _id
            symbol
            name
            baseCurrency
            quoteCurrency
            logoUrl
            isActive
            createdAt
            updatedAt
        }
    }
`;

export const REMOVE_ASSET = gql`
    mutation RemoveAsset($symbol: String!) {
        removeAsset(symbol: $symbol)
    }
`;

export const UPDATE_INTERVAL = gql`
    mutation UpdateInterval($interval: String!) {
        updateInterval(interval: $interval) {
            selectedInterval
        }
    }
`;

export const UPDATE_INDICATORS = gql`
    mutation UpdateIndicators($indicators: [String!]!) {
        updateIndicators(indicators: $indicators) {
            selectedIndicators
        }
    }
`;

export const UPDATE_EXCHANGE = gql`
    mutation UpdateExchange($exchange: String!) {
        updateExchange(exchange: $exchange) {
            selectedExchange
        }
    }
`;

export const UPDATE_ASSET_POSITIONS = gql`
    mutation UpdateAssetPositions($positions: [AssetPositionInput!]!) {
        updateAssetPositions(positions: $positions)
    }
`;

export const UPDATE_SELECTED_CATEGORY = gql`
    mutation UpdateSelectedCategory($category: String!) {
        updateSelectedCategory(category: $category) {
            selectedCategory
        }
    }
`;

export const UPDATE_ASSET_CATEGORY = gql`
    mutation UpdateAssetCategory($symbol: String!, $category: String) {
        updateAssetCategory(symbol: $symbol, category: $category)
    }
`;

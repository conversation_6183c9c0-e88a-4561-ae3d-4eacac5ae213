import {gql} from '@apollo/client';

export const GET_AVAILABLE_ASSETS = gql`
    query GetAvailableAssets {
        availableAssets {
            symbol
            name
            baseCurrency
            quoteCurrency
            logoUrl
            isActive
        }
    }
`;

export const GET_WATCHED_ASSETS = gql`
    query GetWatchedAssets {
        watchedAssets {
            _id
            symbol
            name
            baseCurrency
            quoteCurrency
            logoUrl
            isActive
            exchange
            category
            createdAt
            updatedAt
        }
    }
`;

export const GET_USER_SETTINGS = gql`
    query GetUserSettings {
        getUserSettings {
            selectedInterval
            selectedIndicators
            selectedExchange
            selectedCategory
        }
    }
`;

export const GET_AVAILABLE_EXCHANGES = gql`
    query GetAvailableExchanges {
        availableExchanges
    }
`;

export const GET_AVAILABLE_CATEGORIES = gql`
    query GetAvailableCategories {
        availableCategories
    }
`;
export const GET_DASHBOARD_ROWS = gql`
    query GetDashboardRows {
        dashboardRows {
            symbol
            price
            change24h
            volume24h
            recommendation
            confidence
            indicators {
                indicator
                value
                signal
            }
        }
    }
`;

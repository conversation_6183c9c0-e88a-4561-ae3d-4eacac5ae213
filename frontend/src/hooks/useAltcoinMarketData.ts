import { useQuery } from '@apollo/client';
import { gql } from '@apollo/client';

const ALTCOIN_MARKET_DATA_QUERY = gql`
  query AltcoinMarketData {
    altcoinMarketData {
      btcDominance
      altcoinSeasonIndex
      fearGreedIndex
      marketCapRatio
      lastUpdate
      signals
    }
  }
`;

export interface AltcoinMarketData {
  btcDominance?: number;
  altcoinSeasonIndex?: number;
  fearGreedIndex?: number;
  marketCapRatio?: number;
  lastUpdate: Date;
  signals: {
    btcDominanceSignal: 'BUY' | 'SELL' | 'NEUTRAL';
    altcoinSeasonSignal: 'BUY' | 'SELL' | 'NEUTRAL';
    fearGreedSignal: 'BUY' | 'SELL' | 'NEUTRAL';
    marketCapRatioSignal: 'BUY' | 'SELL' | 'NEUTRAL';
  };
}

export function useAltcoinMarketData() {
  const { data, loading, error, refetch } = useQuery(ALTCOIN_MARKET_DATA_QUERY, {
    pollInterval: 30000, // Poll every 30 seconds
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
  });

  return {
    marketData: data?.altcoinMarketData as AltcoinMarketData | null,
    loading,
    error,
    refetch,
  };
}

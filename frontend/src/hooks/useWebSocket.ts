import {useEffect, useState} from 'react';
import {io, Socket} from 'socket.io-client';

interface SchedulerStatus {
    status: 'started' | 'completed';
    timestamp: string;
    details?: {
        assetsProcessed?: number;
        exchanges?: number;
        intervals?: number;
        error?: string;
    };
}

interface AssetProgress {
    current: number;
    total: number;
    asset?: string;
    exchange?: string;
    timestamp: string;
}

export function useWebSocket() {
    const [socket, setSocket] = useState<Socket | null>(null);
    const [isConnected, setIsConnected] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [lastUpdate, setLastUpdate] = useState<string | null>(null);

    useEffect(() => {
        // Create socket connection
        const socketUrl = process.env.NEXT_PUBLIC_SOCKET_BACKEND || 'http://localhost:3001';
        console.log('Connecting to WebSocket:', socketUrl);

        const newSocket = io(socketUrl, {
            transports: ['websocket'],
        });

        newSocket.on('connect', () => {
            console.log('WebSocket connected');
            setIsConnected(true);
        });

        newSocket.on('disconnect', () => {
            console.log('WebSocket disconnected');
            setIsConnected(false);
            setIsUpdating(false);
        });

        newSocket.on('scheduler-status', (data: SchedulerStatus) => {
            console.log('Scheduler status:', data);

            if (data.status === 'started') {
                setIsUpdating(true);
            } else if (data.status === 'completed') {
                setIsUpdating(false);
                setLastUpdate(data.timestamp);
            }
        });

        newSocket.on('asset-progress', (data: AssetProgress) => {
            console.log('Asset progress:', data);
            // Could be used for more detailed progress indication
        });

        setSocket(newSocket);

        // Cleanup on unmount
        return () => {
            newSocket.close();
        };
    }, []);

    return {
        socket,
        isConnected,
        isUpdating,
        lastUpdate,
    };
}

import React, { useState } from 'react';

interface CategoryTabsProps {
    categories: string[];
    selectedCategory: string;
    onCategoryChange: (category: string) => void;
    onAssetDropped?: (asset: string, category: string) => void;
}

export function CategoryTabs({ categories, selectedCategory, onCategoryChange, onAssetDropped }: CategoryTabsProps) {
    const [dragOverCategory, setDragOverCategory] = useState<string | null>(null);
    const getCategoryDisplayName = (category: string): string => {
        if (category === 'all') return 'All';
        return category.charAt(0).toUpperCase() + category.slice(1);
    };

    const handleDragOver = (e: React.DragEvent, category: string) => {
        e.preventDefault();
        setDragOverCategory(category);
    };

    const handleDragLeave = () => {
        setDragOverCategory(null);
    };

    const handleDrop = (e: React.DragEvent, category: string) => {
        e.preventDefault();
        setDragOverCategory(null);

        const assetSymbol = e.dataTransfer.getData('text/plain');
        if (assetSymbol && onAssetDropped) {
            // Don't allow dropping on 'all' category
            if (category !== 'all') {
                onAssetDropped(assetSymbol, category);
            }
        }
    };

    const canAcceptDrop = (category: string) => {
        return category !== 'all';
    };

    const handleNewCategoryDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOverCategory(null);

        const assetSymbol = e.dataTransfer.getData('text/plain');
        if (assetSymbol && onAssetDropped) {
            const newCategory = prompt('Enter new category name:');
            if (newCategory && newCategory.trim()) {
                onAssetDropped(assetSymbol, newCategory.trim());
            }
        }
    };

    return (
        <div className="category-tabs">
            {categories.map((category) => (
                <button
                    key={category}
                    className={`category-tab ${selectedCategory === category ? 'active' : ''} ${dragOverCategory === category ? 'drag-over' : ''} ${!canAcceptDrop(category) ? 'no-drop' : ''}`}
                    onClick={() => onCategoryChange(category)}
                    onDragOver={(e) => canAcceptDrop(category) ? handleDragOver(e, category) : e.preventDefault()}
                    onDragLeave={handleDragLeave}
                    onDrop={(e) => handleDrop(e, category)}
                    style={{
                        opacity: !canAcceptDrop(category) && dragOverCategory ? 0.5 : 1
                    }}
                >
                    {getCategoryDisplayName(category)}
                </button>
            ))}

            {/* New Category Drop Zone */}
            <button
                className={`category-tab new-category ${dragOverCategory === 'new' ? 'drag-over' : ''}`}
                onDragOver={(e) => handleDragOver(e, 'new')}
                onDragLeave={handleDragLeave}
                onDrop={handleNewCategoryDrop}
                onClick={() => {
                    const newCategory = prompt('Enter new category name:');
                    if (newCategory && newCategory.trim()) {
                        onCategoryChange(newCategory.trim());
                    }
                }}
            >
                + New Category
            </button>
        </div>
    );
}

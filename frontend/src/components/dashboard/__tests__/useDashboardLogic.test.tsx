import { renderHook, act } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';
import { useDashboardLogic } from '../hooks/useDashboardLogic';
import { GET_WATCHED_ASSETS } from '@/lib/graphql/queries';
import { ADD_ASSET, REMOVE_ASSET } from '@/lib/graphql/mutations';

const mocks = [
    {
        request: {
            query: GET_WATCHED_ASSETS,
        },
        result: {
            data: {
                watchedAssets: [
                    { symbol: 'BTC-USDT' },
                    { symbol: 'ETH-USDT' },
                ],
            },
        },
    },
    {
        request: {
            query: ADD_ASSET,
            variables: { symbol: 'SOL-USDT' },
        },
        result: {
            data: {
                addAsset: { symbol: 'SOL-USDT', name: 'Solana' },
            },
        },
    },
    {
        request: {
            query: REMOVE_ASSET,
            variables: { symbol: 'BTC-USDT' },
        },
        result: {
            data: {
                removeAsset: { success: true },
            },
        },
    },
];

const wrapper = ({ children }: { children: React.ReactNode }) => (
    <MockedProvider mocks={mocks} addTypename={false}>
        {children}
    </MockedProvider>
);

describe('useDashboardLogic', () => {
    it('initializes with default values', () => {
        const { result } = renderHook(() => useDashboardLogic(), { wrapper });

        expect(result.current.selectedAssets).toEqual([]);
        expect(result.current.selectedInterval).toBe('1h');
        expect(result.current.selectedIndicators).toEqual(['RSI', 'MACD', 'BB']);
        expect(result.current.isAssetSelectorOpen).toBe(false);
        expect(result.current.availableIntervals).toEqual(['15m', '1h', '4h', '1d']);
        expect(result.current.availableIndicators).toEqual([
            'RSI', 'MACD', 'BB', 'STOCH', 'AO', 'OBV', 'EMA_12', 'EMA_26'
        ]);
    });

    it('handles interval change', () => {
        const { result } = renderHook(() => useDashboardLogic(), { wrapper });

        act(() => {
            result.current.handleIntervalChange('4h');
        });

        expect(result.current.selectedInterval).toBe('4h');
    });

    it('handles indicator toggle', () => {
        const { result } = renderHook(() => useDashboardLogic(), { wrapper });

        // Add indicator
        act(() => {
            result.current.handleIndicatorToggle('STOCH');
        });

        expect(result.current.selectedIndicators).toContain('STOCH');

        // Remove indicator
        act(() => {
            result.current.handleIndicatorToggle('RSI');
        });

        expect(result.current.selectedIndicators).not.toContain('RSI');
    });

    it('handles asset selector modal', () => {
        const { result } = renderHook(() => useDashboardLogic(), { wrapper });

        act(() => {
            result.current.openAssetSelector();
        });

        expect(result.current.isAssetSelectorOpen).toBe(true);

        act(() => {
            result.current.closeAssetSelector();
        });

        expect(result.current.isAssetSelectorOpen).toBe(false);
    });

    it('handles asset addition', async () => {
        const { result } = renderHook(() => useDashboardLogic(), { wrapper });

        await act(async () => {
            await result.current.handleAssetAdd('SOL-USDT');
        });

        // The actual asset list update would happen through the GraphQL refetch
        // This test verifies the function can be called without errors
        expect(result.current.handleAssetAdd).toBeDefined();
    });

    it('handles asset removal', async () => {
        const { result } = renderHook(() => useDashboardLogic(), { wrapper });

        await act(async () => {
            await result.current.handleAssetRemove('BTC-USDT');
        });

        // The actual asset list update would happen through the GraphQL refetch
        // This test verifies the function can be called without errors
        expect(result.current.handleAssetRemove).toBeDefined();
    });
});

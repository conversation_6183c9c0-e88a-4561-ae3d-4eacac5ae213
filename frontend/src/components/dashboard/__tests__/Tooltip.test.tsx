import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Tooltip } from '../Tooltip';

describe('Tooltip', () => {
    it('renders children correctly', () => {
        render(
            <Tooltip content="Test tooltip content">
                <button>Hover me</button>
            </Tooltip>
        );

        expect(screen.getByText('Hover me')).toBeInTheDocument();
    });

    it('shows tooltip on mouse enter', () => {
        render(
            <Tooltip content="Test tooltip content">
                <button>Hover me</button>
            </Tooltip>
        );

        const button = screen.getByText('Hover me');
        fireEvent.mouseEnter(button.parentElement!);

        expect(screen.getByText('Test tooltip content')).toBeInTheDocument();
    });

    it('hides tooltip on mouse leave', () => {
        render(
            <Tooltip content="Test tooltip content">
                <button>Hover me</button>
            </Tooltip>
        );

        const button = screen.getByText('Hover me');
        const container = button.parentElement!;

        fireEvent.mouseEnter(container);
        expect(screen.getByText('Test tooltip content')).toBeInTheDocument();

        fireEvent.mouseLeave(container);
        expect(screen.queryByText('Test tooltip content')).not.toBeInTheDocument();
    });

    it('applies correct positioning styles', () => {
        render(
            <Tooltip content="Test tooltip content" position="top">
                <button>Hover me</button>
            </Tooltip>
        );

        const button = screen.getByText('Hover me');
        fireEvent.mouseEnter(button.parentElement!);

        const tooltip = screen.getByText('Test tooltip content');
        expect(tooltip).toHaveStyle({
            position: 'absolute',
            bottom: '100%',
            left: '50%',
            transform: 'translateX(-50%)',
        });
    });

    it('handles long content correctly', () => {
        const longContent = 'This is a very long tooltip content that should wrap properly and not exceed the maximum width of 300px as specified in the component styles.';
        
        render(
            <Tooltip content={longContent}>
                <button>Hover me</button>
            </Tooltip>
        );

        const button = screen.getByText('Hover me');
        fireEvent.mouseEnter(button.parentElement!);

        const tooltip = screen.getByText(longContent);
        expect(tooltip).toHaveStyle({
            maxWidth: '300px',
            whiteSpace: 'normal',
        });
    });
});

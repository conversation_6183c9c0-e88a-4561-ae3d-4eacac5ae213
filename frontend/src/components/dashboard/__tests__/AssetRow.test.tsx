import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AssetRow } from '../AssetRow';

const mockRowData = {
    symbol: 'BTC-USDT',
    price: 50000,
    change24h: 2.5,
    volume24h: 1000000,
    priceDiff: 1.8,
    recommendation: 'BUY',
    confidence: 85,
    indicators: [
        { indicator: 'RSI', value: 65.5, signal: 'NEUTRAL' as const },
        { indicator: 'MACD', value: 0.0025, signal: 'BUY' as const },
    ]
};

describe('AssetRow', () => {
    it('renders loading state correctly', () => {
        render(
            <AssetRow
                asset="BTC-USDT"
                indicators={['RSI', 'MACD']}
                rowData={undefined}
                loading={true}
            />
        );

        expect(screen.getByText('BTC-USDT')).toBeInTheDocument();
        expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('renders asset data correctly', () => {
        render(
            <AssetRow
                asset="BTC-USDT"
                indicators={['RSI', 'MACD']}
                rowData={mockRowData}
                loading={false}
            />
        );

        expect(screen.getByText('BTC-USDT')).toBeInTheDocument();
        expect(screen.getByText('$50000.00')).toBeInTheDocument();
        expect(screen.getByText('+1.80%')).toBeInTheDocument();
        expect(screen.getByText('BUY')).toBeInTheDocument();
        expect(screen.getByText('85%')).toBeInTheDocument();
    });

    it('formats prices correctly', () => {
        const lowPriceData = { ...mockRowData, price: 0.000123 };
        const { rerender } = render(
            <AssetRow
                asset="SHIB-USDT"
                indicators={['RSI']}
                rowData={lowPriceData}
                loading={false}
            />
        );

        expect(screen.getByText('$0.000123')).toBeInTheDocument();

        const midPriceData = { ...mockRowData, price: 50.1234 };
        rerender(
            <AssetRow
                asset="ETH-USDT"
                indicators={['RSI']}
                rowData={midPriceData}
                loading={false}
            />
        );

        expect(screen.getByText('$50.1234')).toBeInTheDocument();
    });

    it('displays indicator values and signals correctly', () => {
        render(
            <AssetRow
                asset="BTC-USDT"
                indicators={['RSI', 'MACD']}
                rowData={mockRowData}
                loading={false}
            />
        );

        expect(screen.getByText('65.5')).toBeInTheDocument();
        expect(screen.getByText('NEUTRAL')).toBeInTheDocument();
        expect(screen.getByText('0.0025')).toBeInTheDocument();
        expect(screen.getByText('BUY')).toBeInTheDocument();
    });

    it('handles missing indicator data gracefully', () => {
        const dataWithMissingIndicator = {
            ...mockRowData,
            indicators: [{ indicator: 'RSI', value: 65.5, signal: 'NEUTRAL' as const }]
        };

        render(
            <AssetRow
                asset="BTC-USDT"
                indicators={['RSI', 'MACD', 'BB']}
                rowData={dataWithMissingIndicator}
                loading={false}
            />
        );

        expect(screen.getByText('65.5')).toBeInTheDocument();
        expect(screen.getAllByText('--')).toHaveLength(4); // 2 for MACD, 2 for BB
    });

    it('applies correct signal colors', () => {
        render(
            <AssetRow
                asset="BTC-USDT"
                indicators={['RSI', 'MACD']}
                rowData={mockRowData}
                loading={false}
            />
        );

        const neutralSignal = screen.getByText('NEUTRAL');
        const buySignal = screen.getByText('BUY');

        expect(neutralSignal).toHaveStyle({ color: '#eab308' });
        expect(buySignal).toHaveStyle({ color: '#22c55e' });
    });
});

import React, { useState } from 'react';

interface TooltipProps {
    content: string;
    children: React.ReactNode;
    position?: 'top' | 'bottom' | 'left' | 'right';
}

export function Tooltip({ content, children, position = 'bottom' }: TooltipProps) {
    const [isVisible, setIsVisible] = useState(false);

    const getTooltipStyles = () => {
        const baseStyles = {
            position: 'absolute' as const,
            backgroundColor: 'var(--background)',
            border: '1px solid var(--border)',
            borderRadius: '4px',
            padding: '8px 12px',
            fontSize: '12px',
            color: 'var(--foreground)',
            zIndex: 1000,
            maxWidth: '300px',
            width: '300px',
            whiteSpace: 'normal' as const,
            lineHeight: '1.4',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        };

        switch (position) {
            case 'top':
                return {
                    ...baseStyles,
                    bottom: '100%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    marginBottom: '8px',
                };
            case 'bottom':
                return {
                    ...baseStyles,
                    top: '100%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    marginTop: '8px',
                };
            case 'left':
                return {
                    ...baseStyles,
                    right: '100%',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    marginRight: '8px',
                };
            case 'right':
                return {
                    ...baseStyles,
                    left: '100%',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    marginLeft: '8px',
                };
            default:
                return baseStyles;
        }
    };

    return (
        <div
            style={{ position: 'relative', display: 'inline-block' }}
            onMouseEnter={() => setIsVisible(true)}
            onMouseLeave={() => setIsVisible(false)}
        >
            {children}
            {isVisible && (
                <div style={getTooltipStyles()}>
                    {content}
                </div>
            )}
        </div>
    );
}

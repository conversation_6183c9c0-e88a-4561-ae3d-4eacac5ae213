import React from 'react';
import { CircularGauge } from './CircularGauge';

interface AltcoinMarketData {
  btcDominance?: number;
  altcoinSeasonIndex?: number;
  fearGreedIndex?: number;
  marketCapRatio?: number;
  lastUpdate: Date;
  signals: {
    btcDominanceSignal: 'BUY' | 'SELL' | 'NEUTRAL';
    altcoinSeasonSignal: 'BUY' | 'SELL' | 'NEUTRAL';
    fearGreedSignal: 'BUY' | 'SELL' | 'NEUTRAL';
    marketCapRatioSignal: 'BUY' | 'SELL' | 'NEUTRAL';
  };
}

interface AltcoinMarketBarProps {
  marketData: AltcoinMarketData | null;
}

export function AltcoinMarketBar({ marketData }: AltcoinMarketBarProps) {
  if (!marketData) {
    return (
      <div className="altcoin-market-bar loading">
        <div className="market-indicator">
          <span className="indicator-label">Market Data</span>
          <span className="indicator-value">Loading...</span>
        </div>
      </div>
    );
  }

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'BUY':
        return '#00ff00';
      case 'SELL':
        return '#ff0000';
      case 'NEUTRAL':
        return '#ffff00';
      default:
        return '#ffffff';
    }
  };

  const getFearGreedLabel = (value: number) => {
    if (value <= 20) return 'Extreme Fear';
    if (value <= 40) return 'Fear';
    if (value <= 60) return 'Neutral';
    if (value <= 80) return 'Greed';
    return 'Extreme Greed';
  };

  const getAltcoinSeasonLabel = (value: number) => {
    if (value >= 70) return 'Strong Alt Season';
    if (value >= 55) return 'Alt Season';
    if (value >= 45) return 'Mixed';
    if (value >= 30) return 'BTC Season';
    return 'Strong BTC Season';
  };

  return (
    <div className="altcoin-market-bar">
      {/* Fear & Greed Index */}
      {marketData.fearGreedIndex !== undefined && (
        <CircularGauge
          title="FEAR & GREED"
          value={marketData.fearGreedIndex}
          min={0}
          max={100}
          size={60}
          strokeWidth={4}
          formatValue={(value) => value.toString()}
        />
      )}

      {/* Bitcoin Dominance */}
      {marketData.btcDominance !== undefined && (
        <CircularGauge
          title="BTC DOMINANCE"
          value={marketData.btcDominance}
          min={30}
          max={80}
          size={60}
          strokeWidth={4}
          formatValue={(value) => `${value.toFixed(1)}%`}
        />
      )}

      {/* Altcoin Season Index */}
      {marketData.altcoinSeasonIndex !== undefined && (
        <CircularGauge
          title="ALT SEASON"
          value={marketData.altcoinSeasonIndex}
          min={0}
          max={100}
          size={60}
          strokeWidth={4}
          formatValue={(value) => value.toFixed(0)}
        />
      )}

      {/* Market Cap Ratio */}
      {marketData.marketCapRatio !== undefined && (
        <CircularGauge
          title="ALT/BTC RATIO"
          value={marketData.marketCapRatio}
          min={0.5}
          max={2.0}
          size={60}
          strokeWidth={4}
          formatValue={(value) => `${value.toFixed(2)}x`}
          getColorByValue={(value) => {
            if (value >= 1.5) return '#4caf50'; // Green
            if (value >= 1.0) return '#66bb6a'; // Light Green
            if (value >= 0.8) return '#ffeb3b'; // Yellow
            if (value >= 0.6) return '#ffa726'; // Orange
            return '#ff6b6b'; // Red
          }}
        />
      )}

      {/* Last Update */}
      <div className="market-indicator last-update">
        <span className="indicator-label">Updated</span>
        <span className="indicator-value">
          {new Date(marketData.lastUpdate).toLocaleTimeString()}
        </span>
      </div>
    </div>
  );
}

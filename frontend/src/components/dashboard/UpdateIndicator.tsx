import React from 'react';
import {useWebSocket} from '@/hooks/useWebSocket';

interface UpdateIndicatorProps {
    className?: string;
}

export function UpdateIndicator({className = ''}: UpdateIndicatorProps) {
    const {isConnected, isUpdating, lastUpdate} = useWebSocket();

    if (!isConnected) {
        return null; // Don't show anything if not connected
    }

    return (
        <div className={`flex items-center gap-2 ${className}`}
             style={{color: 'var(--muted)'}}>
            {isUpdating ? (
                <>
                    <div
                        className="w-2 h-2 rounded-full"
                        style={{backgroundColor: 'var(--muted)'}}
                    />
                    <span
                        className="text-xs font-mono"
                        style={{color: 'var(--muted)'}}
                    >
            Updating...
          </span>
                </>
            ) : (
                <>
                    <div
                        className="w-2 h-2 rounded-full opacity-50"
                        style={{backgroundColor: 'var(--muted)'}}
                    />
                    <span
                        className="text-xs font-mono opacity-50"
                        style={{color: 'var(--muted)'}}
                    >
            {lastUpdate ? `Updated ${formatTimeAgo(lastUpdate)}` : 'Ready'}
          </span>
                </>
            )}
        </div>
    );
}

function formatTimeAgo(timestamp: string): string {
    const now = new Date();
    const updateTime = new Date(timestamp);
    const diffMs = now.getTime() - updateTime.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);

    if (diffSeconds < 60) {
        return `${diffSeconds}s ago`;
    } else if (diffMinutes < 60) {
        return `${diffMinutes}m ago`;
    } else {
        const diffHours = Math.floor(diffMinutes / 60);
        return `${diffHours}h ago`;
    }
}

import React from 'react';

interface ChartIndicatorProps {
    label: string;
    value: number;
    min?: number;
    max?: number;
    signal: 'BUY' | 'SELL' | 'NEUTRAL';
    formatValue?: (value: number) => string;
    getColorByValue?: (value: number) => string;
}

export function ChartIndicator({ 
    label, 
    value, 
    min = 0, 
    max = 100, 
    signal, 
    formatValue,
    getColorByValue 
}: ChartIndicatorProps) {
    const percentage = Math.max(0, Math.min(100, ((value - min) / (max - min)) * 100));
    
    const getSignalColor = (signal: string) => {
        switch (signal) {
            case 'BUY':
                return 'var(--accent)';
            case 'SELL':
                return 'var(--danger)';
            case 'NEUTRAL':
                return 'var(--warning)';
            default:
                return 'var(--foreground)';
        }
    };

    const getDefaultColorByValue = (value: number) => {
        if (label.includes('Fear')) {
            // Fear & Greed Index colors
            if (value <= 20) return '#ff4444'; // Extreme Fear - Red
            if (value <= 40) return '#ff8844'; // Fear - Orange
            if (value <= 60) return '#ffff44'; // Neutral - Yellow
            if (value <= 80) return '#88ff44'; // Greed - Light Green
            return '#44ff44'; // Extreme Greed - Green
        } else if (label.includes('Alt Season')) {
            // Altcoin Season colors
            if (value >= 70) return '#44ff44'; // Strong Alt Season - Green
            if (value >= 55) return '#88ff44'; // Alt Season - Light Green
            if (value >= 45) return '#ffff44'; // Mixed - Yellow
            if (value >= 30) return '#ff8844'; // BTC Season - Orange
            return '#ff4444'; // Strong BTC Season - Red
        } else {
            // BTC Dominance colors (inverted - lower dominance is better for alts)
            if (value <= 40) return '#44ff44'; // Low dominance - Green
            if (value <= 50) return '#88ff44'; // Medium-low - Light Green
            if (value <= 60) return '#ffff44'; // Medium - Yellow
            if (value <= 70) return '#ff8844'; // High - Orange
            return '#ff4444'; // Very high - Red
        }
    };

    const barColor = getColorByValue ? getColorByValue(value) : getDefaultColorByValue(value);
    const signalColor = getSignalColor(signal);

    return (
        <div className="chart-indicator">
            <div className="chart-indicator-header">
                <span className="chart-indicator-label">{label}</span>
                <span 
                    className="chart-indicator-value"
                    style={{ color: barColor }}
                >
                    {formatValue ? formatValue(value) : value.toFixed(1)}
                </span>
            </div>
            
            <div className="chart-indicator-bar-container">
                <div 
                    className="chart-indicator-bar-background"
                    style={{ backgroundColor: 'var(--highlight)' }}
                >
                    <div 
                        className="chart-indicator-bar-fill"
                        style={{ 
                            width: `${percentage}%`,
                            backgroundColor: barColor,
                            transition: 'width 0.3s ease, background-color 0.3s ease'
                        }}
                    />
                    <div 
                        className="chart-indicator-bar-marker"
                        style={{ 
                            left: `${percentage}%`,
                            backgroundColor: barColor
                        }}
                    />
                </div>
                <div className="chart-indicator-scale">
                    <span style={{ color: 'var(--muted)' }}>{min}</span>
                    <span style={{ color: 'var(--muted)' }}>{max}</span>
                </div>
            </div>
            
            <div className="chart-indicator-signal">
                <span 
                    className="chart-indicator-signal-text"
                    style={{ color: signalColor }}
                >
                    {signal}
                </span>
            </div>
        </div>
    );
}

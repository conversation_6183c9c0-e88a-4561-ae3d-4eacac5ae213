import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import { GET_AVAILABLE_ASSETS } from '@/lib/graphql/queries';

interface AssetSelectorModalProps {
    isOpen: boolean;
    onClose: () => void;
    onAssetAdd: (symbol: string) => void;
    embedded?: boolean;
}

export function AssetSelectorModal({ isOpen, onClose, onAssetAdd, embedded = false }: AssetSelectorModalProps) {
    const [searchTerm, setSearchTerm] = useState('');
    
    const { data: availableAssets, loading } = useQuery(GET_AVAILABLE_ASSETS, {
        skip: !isOpen,
    });

    const filteredAssets = availableAssets?.availableAssets?.filter((asset: {symbol: string; name: string}) =>
        asset.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.name.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];

    if (!isOpen) return null;

    if (embedded) {
        return (
            <div className="mb-3">
                <input
                    type="text"
                    placeholder="Search assets..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-2 py-1 text-xs border mb-2"
                    style={{
                        borderColor: 'var(--border)',
                        backgroundColor: 'var(--background)',
                        color: 'var(--foreground)'
                    }}
                />
                <div className="max-h-24 overflow-y-auto space-y-1">
                    {loading ? (
                        <div className="text-xs" style={{color: 'var(--muted)'}}>Loading assets...</div>
                    ) : (
                        filteredAssets.slice(0, 5).map((asset: {symbol: string; name: string}) => (
                            <div
                                key={asset.symbol}
                                className="flex items-center justify-between py-1 px-2 text-xs cursor-pointer border"
                                style={{borderColor: 'var(--border)'}}
                                onClick={() => {
                                    onAssetAdd(asset.symbol);
                                    setSearchTerm('');
                                }}
                                onMouseEnter={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--highlight)'}
                                onMouseLeave={(e) => (e.target as HTMLElement).style.backgroundColor = 'transparent'}
                            >
                                <span style={{color: 'var(--foreground)'}}>{asset.symbol}</span>
                                <span style={{color: 'var(--muted)', fontSize: '10px'}}>{asset.name}</span>
                            </div>
                        ))
                    )}
                </div>
            </div>
        );
    }

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h3>Add Crypto Asset</h3>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>

                <div className="modal-body">
                    <div className="search-container">
                        <input
                            type="text"
                            placeholder="Search assets..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="search-input"
                            autoFocus
                        />
                    </div>

                    <div className="assets-list">
                        {loading ? (
                            <div className="loading">Loading assets...</div>
                        ) : (
                            filteredAssets.slice(0, 8).map((asset: {symbol: string; name: string}) => (
                                <div
                                    key={asset.symbol}
                                    className="asset-item"
                                    onClick={() => {
                                        onAssetAdd(asset.symbol);
                                        onClose();
                                    }}
                                    onMouseEnter={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--highlight)'}
                                    onMouseLeave={(e) => (e.target as HTMLElement).style.backgroundColor = 'transparent'}
                                >
                                    <div className="asset-symbol">{asset.symbol}</div>
                                    <div className="asset-name">{asset.name}</div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

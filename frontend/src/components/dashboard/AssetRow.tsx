import React, { useState, useEffect } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface IndicatorResult {
    symbol?: string;
    indicator: string;
    value: number;
    signal: 'BUY' | 'SELL' | 'NEUTRAL';
    timestamp?: string;
}

interface DashboardRowData {
    symbol: string;
    price: number;
    change24h: number;
    volume24h: number;
    recommendation: string;
    confidence: number;
    indicators: IndicatorResult[];
}

interface AssetRowProps {
    asset: string;
    indicators: string[];
    rowData?: DashboardRowData;
    loading: boolean;
    onOpenChart?: (asset: string) => void;
}

export function AssetRow({asset, indicators, rowData, loading, onOpenChart}: AssetRowProps) {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth <= 768);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: asset });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    const indicatorResults: IndicatorResult[] = rowData?.indicators || [];
    const recommendationData = rowData ? {
        recommendation: rowData.recommendation,
        confidence: rowData.confidence
    } : null;

    const formatPrice = (price: number) => {
        if (price < 1) return price.toFixed(6);
        if (price < 100) return price.toFixed(4);
        return price.toFixed(2);
    };

    const formatValue = (value: number, indicator: string) => {
        if (indicator === 'RSI' || indicator === 'STOCH') {
            return value.toFixed(1);
        }
        if (indicator === 'MACD') {
            return value.toFixed(4);
        }
        if (indicator === 'BB') {
            return value.toFixed(2);
        }
        if (indicator === 'AO') {
            return value.toFixed(4);
        }
        if (indicator === 'OBV') {
            return (value / 1000000).toFixed(2) + 'M';
        }
        return value.toFixed(2);
    };

    const getSignalColor = (signal: string) => {
        switch (signal) {
            case 'BUY':
                return '#00ff00';
            case 'SELL':
                return '#ff0000';
            case 'NEUTRAL':
                return '#ffff00';
            default:
                return '#ffffff';
        }
    };

    const getRecommendationColor = (recommendation: string) => {
        if (recommendation.includes('BUY')) return '#00ff00';
        if (recommendation.includes('SELL')) return '#ff0000';
        return '#ffff00';
    };

    // Mobile card layout
    if (isMobile) {
        return (
            <div
                ref={setNodeRef}
                style={style}
                className="asset-row"
                {...attributes}
                onClick={(e) => {
                    // Prevent clicks that start in the drag handle
                    if ((e.target as HTMLElement).closest('.mobile-drag-handle')) return;
                    onOpenChart?.(asset);
                }}
            >
                {/* Mobile Header - Condensed with Recommendation */}
                <div className="flex justify-between items-start mb-2">
                    <div
                        className="mobile-drag-handle flex items-center gap-1 cursor-grab active:cursor-grabbing"
                        {...listeners}
                        style={{ color: 'var(--foreground)', fontSize: '14px', fontWeight: 'bold' }}
                    >
                        <span style={{ fontSize: '12px' }}>⋮⋮</span>
                        <span>{asset}</span>
                    </div>

                    {/* Mobile Recommendation - In Header */}
                    {rowData && (
                        <div className="flex items-center gap-2">
                            <div
                                className="px-2 py-1 rounded text-black font-bold text-xs"
                                style={{ backgroundColor: getRecommendationColor(rowData.recommendation) }}
                            >
                                {rowData.recommendation} ({rowData.confidence.toFixed(0)}%)
                            </div>
                        </div>
                    )}

                    {rowData && (
                        <div className="text-right">
                            <div style={{ color: 'var(--foreground)', fontSize: '13px', fontWeight: 'bold' }}>
                                ${formatPrice(rowData.price)}
                            </div>
                            <div style={{
                                color: rowData.change24h >= 0 ? '#00ff00' : '#ff0000',
                                fontSize: '11px'
                            }}>
                                {rowData.change24h >= 0 ? '+' : ''}{rowData.change24h.toFixed(2)}%
                            </div>
                        </div>
                    )}
                </div>

                {/* Mobile Indicators Grid - Condensed */}
                {rowData && (
                    <div className="grid grid-cols-3 gap-1">
                        {indicators.map(indicator => {
                            const indicatorData = rowData.indicators.find(ind => ind.indicator === indicator);
                            return (
                                <div key={indicator} className="text-center p-1 border rounded" style={{ borderColor: 'var(--border)' }}>
                                    <div style={{ color: 'var(--secondary)', fontSize: '8px', lineHeight: '1' }}>
                                        {indicator}
                                    </div>
                                    <div style={{ color: 'var(--foreground)', fontSize: '10px', fontWeight: 'bold', lineHeight: '1.2' }}>
                                        {indicatorData ? formatValue(indicatorData.value, indicator) : 'N/A'}
                                    </div>
                                    <div
                                        style={{
                                            color: indicatorData ? getSignalColor(indicatorData.signal) : '#ffffff',
                                            fontSize: '8px',
                                            fontWeight: 'bold',
                                            lineHeight: '1'
                                        }}
                                    >
                                        {indicatorData ? indicatorData.signal : 'N/A'}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}

                {/* Loading state for mobile */}
                {loading && (
                    <div className="text-center py-4" style={{ color: 'var(--secondary)' }}>
                        Loading...
                    </div>
                )}
            </div>
        );
    }

    if (loading) {
        return (
            <div className="asset-row loading">
                <div className="asset-symbol">{asset}</div>
                <div className="price-info">Loading...</div>
                <div className="recommendation">Loading...</div>
                {indicators.map(indicator => (
                    <div key={indicator} className="indicator-cell">
                        <div className="indicator-value">--</div>
                        <div className="indicator-signal">--</div>
                    </div>
                ))}
            </div>
        );
    }

    return (
        <div
            ref={setNodeRef}
            style={style}
            className="asset-row"
            {...attributes}
            onClick={(e) => {
                // Prevent clicks that start in the drag handle cell
                if ((e.target as HTMLElement).closest('.asset-info')) return;
                onOpenChart?.(asset);
            }}
        >
            {/* Asset Symbol - draggable */}
            <div
                className="asset-info"
                {...listeners}
                draggable
                onDragStart={(e) => {
                    e.dataTransfer.setData('text/plain', asset);
                    e.dataTransfer.effectAllowed = 'move';
                }}
                style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
            >
                <div className="asset-symbol">
                    {asset.replace('-USDT', '')}
                </div>
            </div>

            {/* Price Info */}
            <div className="price-info">
                {rowData ? (
                    <>
                        <div className="asset-price">${formatPrice(rowData.price)}</div>
                        <div className={
                            rowData.change24h >= 0
                                ? 'price-change-positive'
                                : 'price-change-negative'
                        }>
                            {rowData.change24h >= 0 ? '+' : ''}
                            {rowData.change24h.toFixed(2)}%
                        </div>
                    </>
                ) : (
                    <div style={{color: '#ffffff'}}>Loading...</div>
                )}
            </div>

            {/* Recommendation */}
            <div className={`recommendation-cell ${
                loading || !recommendationData ? '' :
                    recommendationData.recommendation === 'BUY' || recommendationData.recommendation === 'WEAK BUY' ? 'recommendation-buy' :
                        recommendationData.recommendation === 'SELL' || recommendationData.recommendation === 'WEAK SELL' ? 'recommendation-sell' : 'recommendation-hold'
            }`}>
                {loading ? '...' :
                    !recommendationData ? '-' :
                        `${recommendationData.recommendation} (${recommendationData.confidence.toFixed(0)}%)`}
            </div>

            {/* Indicators */}
            {indicators.map(indicator => {
                const result = indicatorResults.find(r => r.indicator === indicator);

                if (loading) {
                    return (
                        <div key={indicator} className="indicator-cell">
                            <span style={{color: 'var(--muted)'}}>...</span>
                        </div>
                    );
                }

                if (!result) {
                    return (
                        <div key={indicator} className="indicator-cell">
                            <span style={{color: 'var(--muted)'}}>-</span>
                        </div>
                    );
                }

                const signalColor = result.signal === 'BUY' ? 'var(--accent)' :
                    result.signal === 'SELL' ? 'var(--danger)' : 'var(--warning)';
                const signalSymbol = result.signal === 'BUY' ? '↑' :
                    result.signal === 'SELL' ? '↓' : '→';

                const cellColor = result.signal === 'BUY' ? 'cell-buy' :
                    result.signal === 'SELL' ? 'cell-sell' : 'var(--warning)';

                return (
                    <div key={indicator} className={"indicator-cell " + cellColor}>
                        <span className="indicator-value">
                            {formatValue(result.value, indicator)}
                        </span>
                        <span style={{color: signalColor, marginLeft: '4px', fontSize: 'large'}}>
                            {signalSymbol}
                        </span>
                    </div>
                );
            })}
        </div>
    );
}

import React from 'react';

interface CircularGaugeProps {
    value: number;
    min?: number;
    max?: number;
    size?: number;
    strokeWidth?: number;
    label?: string;
    title?: string;
    formatValue?: (value: number) => string;
    getColorByValue?: (value: number) => string;
    signal?: 'BUY' | 'SELL' | 'NEUTRAL';
}

export function CircularGauge({
    value,
    min = 0,
    max = 100,
    size = 60,
    strokeWidth = 6,
    label,
    title,
    formatValue,
    getColorByValue,
    signal
}: CircularGaugeProps) {
    const radius = (size - strokeWidth) / 2;
    const centerX = size / 2;
    const centerY = size / 2;
    const percentage = Math.max(0, Math.min(100, ((value - min) / (max - min)) * 100));

    // Arc goes from -135° to +135° (270° total)
    const startAngle = -135; // Start angle in degrees
    const endAngle = startAngle + (270 * percentage / 100); // End angle based on percentage

    // Convert to radians for calculations
    const startRad = (startAngle * Math.PI) / 180;
    const endRad = (endAngle * Math.PI) / 180;

    // Calculate arc path
    const startX = centerX + radius * Math.cos(startRad);
    const startY = centerY + radius * Math.sin(startRad);
    const endX = centerX + radius * Math.cos(endRad);
    const endY = centerY + radius * Math.sin(endRad);

    const largeArcFlag = percentage > 50 ? 1 : 0;

    // Background arc path (full 270°)
    const backgroundEndRad = (startAngle + 270) * Math.PI / 180;
    const backgroundEndX = centerX + radius * Math.cos(backgroundEndRad);
    const backgroundEndY = centerY + radius * Math.sin(backgroundEndRad);

    // Position indicator (small circle at the end of progress)
    const indicatorX = endX;
    const indicatorY = endY;

    const getDefaultColorByValue = (value: number) => {
        if (title?.includes('Fear')) {
            // Fear & Greed Index colors - like the reference image
            if (value <= 25) return '#ff6b6b'; // Red
            if (value <= 45) return '#ffa726'; // Orange
            if (value <= 55) return '#ffeb3b'; // Yellow
            if (value <= 75) return '#66bb6a'; // Light Green
            return '#4caf50'; // Green
        } else if (title?.includes('Alt Season')) {
            // Altcoin Season colors
            if (value >= 70) return '#4caf50'; // Green
            if (value >= 55) return '#66bb6a'; // Light Green
            if (value >= 45) return '#ffeb3b'; // Yellow
            if (value >= 30) return '#ffa726'; // Orange
            return '#ff6b6b'; // Red
        } else if (title?.includes('BTC')) {
            // BTC Dominance colors
            if (value <= 40) return '#4caf50'; // Green
            if (value <= 50) return '#66bb6a'; // Light Green
            if (value <= 60) return '#ffeb3b'; // Yellow
            if (value <= 70) return '#ffa726'; // Orange
            return '#ff6b6b'; // Red
        } else {
            // Alt/BTC Ratio colors
            if (value >= 1.5) return '#4caf50'; // Green
            if (value >= 1.0) return '#66bb6a'; // Light Green
            if (value >= 0.8) return '#ffeb3b'; // Yellow
            if (value >= 0.6) return '#ffa726'; // Orange
            return '#ff6b6b'; // Red
        }
    };

    const getSignalColor = (signal: string) => {
        switch (signal) {
            case 'BUY':
                return 'var(--accent)';
            case 'SELL':
                return 'var(--danger)';
            case 'NEUTRAL':
                return 'var(--warning)';
            default:
                return 'var(--foreground)';
        }
    };

    const gaugeColor = getColorByValue ? getColorByValue(value) : getDefaultColorByValue(value);
    const signalColor = signal ? getSignalColor(signal) : gaugeColor;

    const getFearGreedLabel = (value: number) => {
        if (value <= 20) return 'Extreme Fear';
        if (value <= 40) return 'Fear';
        if (value <= 60) return 'Neutral';
        if (value <= 80) return 'Greed';
        return 'Extreme Greed';
    };

    const getAltcoinSeasonLabel = (value: number) => {
        if (value >= 70) return 'Strong Alt Season';
        if (value >= 55) return 'Alt Season';
        if (value >= 45) return 'Mixed';
        if (value >= 30) return 'BTC Season';
        return 'Strong BTC Season';
    };

    const getDisplayLabel = () => {
        if (title?.includes('Fear')) {
            return getFearGreedLabel(value);
        } else if (title?.includes('Alt Season')) {
            return getAltcoinSeasonLabel(value);
        }
        return label || '';
    };

    // Create gradient ID for this gauge
    const gradientId = `gradient-${Math.random().toString(36).substr(2, 9)}`;

    return (
        <div className="circular-gauge">
            <div className="gauge-container" style={{ width: size, height: size }}>
                <svg width={size} height={size} className="gauge-svg">
                    {/* Define gradient */}
                    <defs>
                        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%" gradientUnits="objectBoundingBox">
                            <stop offset="0%" stopColor="#ff6b6b" />
                            <stop offset="25%" stopColor="#ffa726" />
                            <stop offset="50%" stopColor="#ffeb3b" />
                            <stop offset="75%" stopColor="#66bb6a" />
                            <stop offset="100%" stopColor="#4caf50" />
                        </linearGradient>
                    </defs>

                    {/* Full background arc with gradient */}
                    <path
                        d={`M ${startX} ${startY} A ${radius} ${radius} 0 1 1 ${backgroundEndX} ${backgroundEndY}`}
                        fill="none"
                        stroke={`url(#${gradientId})`}
                        strokeWidth={strokeWidth}
                        strokeLinecap="round"
                        opacity="1"
                    />

                    {/* Position indicator - white circle at exact value position */}
                    {percentage > 0 && (
                        <circle
                            cx={endX}
                            cy={endY}
                            r={2.5}
                            fill="white"
                            stroke="rgba(0,0,0,0.2)"
                            strokeWidth={0.5}
                            className="gauge-indicator"
                            style={{
                                filter: 'drop-shadow(0 0 2px rgba(0,0,0,0.3))'
                            }}
                        />
                    )}
                </svg>

                {/* Center content */}
                <div className="gauge-center">
                    <div className="gauge-value" style={{ color: 'var(--foreground)' }}>
                        {formatValue ? formatValue(value) : Math.round(value)}
                    </div>
                    <div className="gauge-label" style={{ color: 'var(--secondary)' }}>
                        {getDisplayLabel() || title}
                    </div>
                </div>
            </div>
        </div>
    );
}

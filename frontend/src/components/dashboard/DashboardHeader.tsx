import React from 'react';
import { Tooltip } from './Tooltip';

interface DashboardHeaderProps {
    indicators: string[];
}

const INDICATOR_DESCRIPTIONS = {
    'RSI': 'Relative Strength Index (0-100): Measures momentum. Values >70 suggest overbought conditions, <30 suggest oversold conditions.',
    'MACD': 'Moving Average Convergence Divergence: Shows relationship between two moving averages. Positive values suggest upward momentum.',
    'BB': 'Bollinger Bands: Price volatility indicator. Shows if price is relatively high or low compared to recent trades.',
    'STOCH': 'Stochastic Oscillator (0-100): Momentum indicator comparing closing price to price range. >80 overbought, <20 oversold.',
    'AO': 'Awesome Oscillator: Enhanced momentum indicator with increased weight for crypto markets. Particularly reliable for momentum-driven crypto trends.',
    'OBV': 'On-Balance Volume: Uses volume flow to predict price changes. Rising OBV suggests accumulation, falling suggests distribution.',
    'EMA_12': '12-period Exponential Moving Average: Trend-following indicator that gives more weight to recent prices.',
    'EMA_26': '26-period Exponential Moving Average: Longer-term trend indicator, slower to react than EMA_12.',
    // New crypto-specific indicators
    'WILLIAMS_R': 'Williams %R (-100 to 0): Crypto-optimized momentum oscillator. Values >-20 suggest overbought, <-80 suggest oversold conditions.',
    'CCI': 'Commodity Channel Index: Identifies cyclical trends in crypto markets. Values >100 suggest strong uptrend, <-100 suggest strong downtrend.',
    'ADX': 'Average Directional Index (0-100): Measures trend strength in volatile crypto markets. Values >25 indicate strong trends.',
    'MFI': 'Money Flow Index (0-100): Volume-weighted RSI combining price and volume. >80 overbought, <20 oversold with volume confirmation.',
    'VWAP': 'Volume Weighted Average Price: Institutional benchmark showing average price weighted by volume. Price above VWAP is bullish.'
};

export function DashboardHeader({ indicators }: DashboardHeaderProps) {
    return (
        <div className="table-header">
            <div>Asset</div>
            <div>Price</div>
            <div>Recommendation</div>
            {indicators.map(indicator => (
                <Tooltip
                    key={indicator}
                    content={INDICATOR_DESCRIPTIONS[indicator as keyof typeof INDICATOR_DESCRIPTIONS] || indicator}
                    position="bottom"
                >
                    <div style={{ cursor: 'help' }}>
                        {indicator}
                    </div>
                </Tooltip>
            ))}
        </div>
    );
}

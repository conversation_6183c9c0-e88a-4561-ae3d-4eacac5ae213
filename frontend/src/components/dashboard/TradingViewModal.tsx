import React, {useEffect} from 'react';

interface TradingViewModalProps {
    symbol: string | null;
    interval: string;
    exchange: string;
    onClose: () => void;
}

declare global {
    interface Window {
        TradingView?: {
            widget: new (opts: TradingViewWidgetOptions) => TradingViewWidget;
        }
    }
}

type TradingViewWidget = object;

interface TradingViewWidgetOptions {
    symbol: string;
    interval: string;
    container_id: string;
    autosize?: boolean;
    width?: string;
    height?: string;
    theme?: 'light' | 'dark';
    timezone?: string;
    style?: string;
    locale?: string;
    toolbar_bg?: string;
    enable_publishing?: boolean;
    hide_legend?: boolean;
    hide_side_toolbar?: boolean;
    withdateranges?: boolean;
    range?: string;
    allow_symbol_change?: boolean;
    details?: boolean;
    hotlist?: boolean;
    calendar?: boolean;
    studies?: string[];
    show_popup_button?: boolean;
    popup_width?: string;
    popup_height?: string;
}

// Map our interval to TradingView's
function mapInterval(interval: string): string {
    switch (interval) {
        case '15m':
            return '15';
        case '1h':
            return '60';
        case '4h':
            return '240';
        case '1d':
            return 'D';
        default:
            return '60';
    }
}

function mapSymbolToTradingView(symbol: string, exchange: string): string {
    // Example: BTC-USDT -> BTCUSDT, with exchange prefix
    const tvSymbol = symbol.replace('_', '-').replace('-USDT', 'USDT').replace('-', '');
    // Normalize exchange names to TradingView format where possible
    const tvExchange = exchange.toUpperCase();
    return `${tvExchange}:${tvSymbol}`;
}

export function TradingViewModal({symbol, interval, exchange, onClose}: TradingViewModalProps) {
    // Handle Escape key to close modal
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                onClose();
            }
        };

        document.addEventListener('keydown', handleEscape);
        return () => document.removeEventListener('keydown', handleEscape);
    }, [onClose]);

    useEffect(() => {
        if (!symbol) return;

        // Dynamically load TradingView widget script if not present
        const scriptId = 'tradingview-widget-script';
        const onReady = () => {
            if (window.TradingView) initWidget();
        };

        if (!document.getElementById(scriptId)) {
            const script = document.createElement('script');
            script.id = scriptId;
            script.src = 'https://s3.tradingview.com/tv.js';
            script.async = true;
            script.onload = onReady;
            document.body.appendChild(script);
        } else {
            onReady();
        }

        function initWidget() {
            const container = document.getElementById('tv_chart_container');
            if (!container || !symbol || !window.TradingView) return;

            container.innerHTML = '';

            try {
                new window.TradingView.widget({
                    symbol: mapSymbolToTradingView(symbol, exchange),
                    interval: mapInterval(interval),
                    container_id: 'tv_chart_container',
                    autosize: true,
                    width: '100%',
                    height: '100%',
                    theme: 'dark',
                    timezone: 'Etc/UTC',
                    style: '1',
                    locale: 'en',
                    toolbar_bg: 'var(--background)',
                    enable_publishing: false,
                    hide_legend: false,
                    hide_side_toolbar: false,
                    withdateranges: false,
                    allow_symbol_change: true,
                    details: false,
                    hotlist: false,
                    calendar: false,
                    studies: [
                        'AwesomeOscillator@tv-basicstudies',
                        'RSI@tv-basicstudies',
                        'BB@tv-basicstudies'
                    ],
                    show_popup_button: false,
                    popup_width: '1000',
                    popup_height: '650',
                });
            } catch (error) {
                console.error('Failed to initialize TradingView widget:', error);
                if (container) {
                    container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--foreground);">Failed to load TradingView chart. Please try again.</div>';
                }
            }
        }
    }, [symbol, interval, exchange]);

    if (!symbol) return null;

    return (
        <div
            className="fixed inset-0 z-50 flex items-center justify-center  bg-opacity-50 p-2 md:p-4"
            onClick={onClose}
        >
            <div
                className="bg-white dark:bg-gray-900 rounded-lg shadow-xl font-mono relative w-full h-full md:w-auto md:h-auto"
                style={{
                    width: 'min(100vw, 1400px)',
                    minWidth: '300px',
                    minHeight: '400px',
                    maxWidth: '100vw',
                    maxHeight: '100vh',
                    backgroundColor: 'var(--background)',
                    border: '1px solid var(--border)'
                }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Header */}
                <div
                    className="flex items-center justify-between p-2 md:p-4 border-b"
                    style={{borderColor: 'var(--border)'}}
                >
                    <h2 className="text-sm md:text-lg font-semibold truncate" style={{color: 'var(--foreground)'}}>
                        {symbol} Chart
                    </h2>
                    <button
                        onClick={onClose}
                        className="px-2 md:px-3 py-1 text-xs md:text-sm border rounded transition-colors hover:bg-gray-100 dark:hover:bg-gray-800 whitespace-nowrap"
                        style={{
                            borderColor: 'var(--border)',
                            color: 'var(--secondary)',
                            backgroundColor: 'transparent'
                        }}
                        onMouseEnter={(e) => (e.target as HTMLElement).style.backgroundColor = 'var(--highlight)'}
                        onMouseLeave={(e) => (e.target as HTMLElement).style.backgroundColor = 'transparent'}
                    >
                        <span className="md:hidden">✕</span>
                        <span className="hidden md:inline">✕ Close</span>
                    </button>
                </div>

                {/* Chart Container */}
                <div
                    className="p-1 md:p-2"
                    style={{height: 'calc(100% - 50px)'}}
                >
                    <div
                        id="tv_chart_container"
                        style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: '4px',
                            overflow: 'hidden'
                        }}
                    />
                </div>
            </div>
        </div>
    );
}


@import "tailwindcss";

/* Terminal Theme - Based on Screenshot */
:root {
    --background: #2d2d2d;
    --foreground: #ffffff;
    --primary: #ffffff;
    --secondary: #cccccc;
    --accent: #00ff00;
    --warning: #ffff00;
    --danger: #ff6666;
    --border: #555555;
    --muted: #999999;
    --highlight: #404040;
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
}

* {
    scrollbar-width: thin;
    scrollbar-color: var(--secondary) var(--background);
}

*::-webkit-scrollbar {
    width: 8px;
}

*::-webkit-scrollbar-track {
    background: var(--background);
}

*::-webkit-scrollbar-thumb {
    background-color: var(--secondary);
    border-radius: 0px;
}

body {
    background: var(--background);
    color: #ffffff;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    line-height: 1.4;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    body {
        font-size: 11px;
        line-height: 1.3;
    }
}

/* Ultra Compact Dashboard Styles */
.dashboard-table {
    border: 1px solid var(--border);
    width: 100%;
    border-collapse: collapse;
}

.table-header {
    display: grid;
    grid-template-columns: 120px 140px 140px repeat(auto-fit, minmax(100px, 1fr));
    border-bottom: 1px solid var(--border);
    background: var(--highlight);
    min-width: fit-content;
}

/* Mobile responsive table header - hide on mobile */
@media (max-width: 768px) {
    .table-header {
        display: none;
    }
}

.table-header > div {
    padding: 6px 8px;
    border-right: 1px solid var(--border);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #ffffff;
    text-align: center;
}

/* Mobile responsive table header cells */
@media (max-width: 768px) {
    .table-header > div {
        padding: 4px 2px;
        font-size: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.table-header > div:last-child {
    border-right: none;
}

.asset-row {
    display: grid;
    grid-template-columns: 120px 140px 140px repeat(auto-fit, minmax(100px, 1fr));
    border-bottom: 1px solid var(--border);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    height: 36px;
    min-width: fit-content;
    cursor: pointer;
}

/* Mobile responsive asset rows - condensed card layout */
@media (max-width: 768px) {
    .asset-row {
        display: block;
        background: var(--highlight);
        border: 1px solid var(--border);
        border-radius: 6px;
        margin-bottom: 6px;
        padding: 8px;
        height: auto;
        min-height: 44px; /* iOS recommended touch target size */
        font-size: 12px;
    }

    .asset-row:hover {
        background: var(--background);
        border-color: var(--accent);
    }
}

.asset-row:last-child {
    border-bottom: none;
}

.asset-row:hover {
    background: var(--highlight);
}

.asset-info {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 6px;
    border-right: 1px solid var(--border);
    overflow: hidden;
}

/* Mobile responsive asset info */
@media (max-width: 768px) {
    .asset-info {
        padding: 2px 3px;
        font-size: 10px;
    }
}

.asset-symbol {
    color: #ffffff;
    font-weight: bold;
    font-size: 13px;
}

.price-info {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 6px;
    border-right: 1px solid var(--border);
    overflow: hidden;
    gap: 8px;
}

/* Mobile responsive price info */
@media (max-width: 768px) {
    .price-info {
        padding: 2px 3px;
        gap: 4px;
        flex-direction: column;
        font-size: 9px;
    }
}

.asset-price {
    color: #ffffff;
    font-size: 13px;
    line-height: 1.1;
}

.price-change-positive {
    color: var(--accent);
}

.price-change-negative {
    color: var(--danger);
}

.indicator-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 6px;
    border-right: 1px solid var(--border);
    font-size: 12px;
    font-family: monospace;
    text-align: center;
    overflow: hidden;
}

/* Mobile responsive indicator cells */
@media (max-width: 768px) {
    .indicator-cell {
        padding: 2px 1px;
        font-size: 9px;
        flex-direction: column;
        gap: 1px;
    }
}

.indicator-cell:last-child {
    border-right: none;
}

.indicator-value {
    color: #ffffff;
    font-weight: normal;
    font-size: 12px;
}

.signal-buy {
    color: var(--accent);
    font-weight: normal;
    font-size: 14px;
}

.signal-sell {
    color: var(--danger);
    font-weight: normal;
    font-size: 14px;
}

.signal-neutral {
    color: var(--warning);
    font-weight: normal;
    font-size: 14px;
}

.recommendation-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 6px;
    border-right: 1px solid var(--border);
    font-size: 13px;
    font-family: 'Courier New', monospace;
    text-align: center;
    overflow: hidden;
    font-weight: bold;
}

.recommendation-buy {
    color: var(--accent);
}

.recommendation-sell {
    color: var(--danger);
}

.recommendation-hold {
    color: var(--warning);
}

/* Table container for horizontal scrolling */
.table-container {
    overflow-x: auto;
    overflow-y: visible;
    width: 100%;
    border: 1px solid var(--border);
}

.table-wrapper {
    min-width: max-content;
    width: 100%;
}

/* Overlay Menu */
.overlay-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-panel {
    background: var(--background);
    border: 2px solid var(--border);
    padding: 12px;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    font-size: 11px;
}

/* Mobile responsive menu panel */
@media (max-width: 768px) {
    .menu-panel {
        width: 95%;
        max-width: none;
        max-height: 90vh;
        padding: 8px;
        font-size: 10px;
    }
}

.blink {
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

.cell-buy {
    background: rgba(123, 162, 123, 0.58);
}

.cell-sell {
    background: rgba(169, 82, 82, 0.61);
}

#tv_chart_container iframe {
    height: 85vh !important;
}

/* Altcoin Market Bar Styles */
.altcoin-market-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 12px;
    background: var(--highlight);
    border: 1px solid var(--border);
    border-radius: 4px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 11px;
    line-height: 1.2;
    overflow-x: auto;
    white-space: nowrap;
    min-height: 80px;
}

.altcoin-market-bar.loading {
    justify-content: center;
}

.market-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
    margin: 0 8px;
}

.market-indicator.last-update {
    margin-left: auto;
    min-width: 60px;
}

.indicator-label {
    color: var(--secondary);
    font-size: 9px;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.indicator-value {
    color: var(--foreground);
    font-weight: bold;
    font-size: 11px;
    margin-bottom: 1px;
}

.indicator-signal {
    font-size: 8px;
    font-weight: bold;
    text-transform: uppercase;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    /* Mobile-specific padding and margins */
    .min-h-screen {
        padding: 8px !important;
    }

    /* Mobile header adjustments */
    .flex.items-center.justify-between.mb-2 {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    /* Mobile button adjustments */
    button {
        font-size: 12px !important;
        padding: 6px 12px !important;
    }

    /* Mobile TradingView modal */
    .fixed.inset-0.z-50.flex.items-center.justify-center {
        padding: 4px;
    }

    /* Mobile chart container */
    #tv_chart_container iframe {
        height: 80vh !important;
    }

    /* Mobile table scrolling - disable for card layout */
    .table-container {
        overflow-x: visible;
        -webkit-overflow-scrolling: touch;
    }

    /* Mobile text sizing */
    .text-xs {
        font-size: 12px !important;
    }

    .text-sm {
        font-size: 14px !important;
    }

    /* Mobile spacing */
    .gap-4 {
        gap: 8px !important;
    }

    .gap-2 {
        gap: 4px !important;
    }

    /* Mobile modal adjustments */
    .menu-panel {
        margin: 4px;
        border-radius: 8px;
        font-size: 14px !important;
    }

    /* Mobile drag handle */
    .mobile-drag-handle {
        touch-action: none;
        user-select: none;
    }

    /* Mobile grid adjustments - condensed */
    .grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
        gap: 4px;
    }

    .grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
        gap: 2px;
    }

    /* Mobile card hover effects */
    .asset-row:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* Mobile altcoin market bar */
    .altcoin-market-bar {
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
        padding: 12px 8px;
        margin-bottom: 8px;
        font-size: 10px;
    }

    .market-indicator {
        min-width: 60px;
        margin: 2px 4px;
    }

    .market-indicator.last-update {
        margin-left: 4px;
        min-width: 50px;
    }

    .indicator-label {
        font-size: 8px;
    }

    .indicator-value {
        font-size: 10px;
    }

    .indicator-signal {
        font-size: 7px;
    }

    /* Mobile spacing adjustments */
    .mb-2 {
        margin-bottom: 4px !important;
    }

    .mb-3 {
        margin-bottom: 6px !important;
    }

    .p-1 {
        padding: 2px !important;
    }

    .p-2 {
        padding: 4px !important;
    }

    /* Mobile line height adjustments */
    .leading-tight {
        line-height: 1.1 !important;
    }
}

/* Category Tabs Styles */
.category-tabs {
    display: flex;
    align-items: center;
    gap: 0;
    border: 1px solid var(--border);
    background: var(--background);
    font-family: 'Courier New', monospace;
    overflow-x: auto;
    white-space: nowrap;
}

.category-tab {
    background: var(--background);
    color: var(--secondary);
    border: none;
    border-right: 1px solid var(--border);
    padding: 8px 16px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: fit-content;
}

.category-tab:last-child {
    border-right: none;
}

.category-tab:hover {
    background: var(--highlight);
    color: var(--foreground);
}

.category-tab.active {
    background: var(--highlight);
    color: var(--foreground);
    font-weight: bold;
}

.category-tab.drag-over {
    background: var(--accent);
    color: var(--background);
    transform: scale(1.05);
}

.category-tab.new-category {
    background: var(--muted);
    color: var(--background);
    font-style: italic;
}

.category-tab.new-category:hover {
    background: var(--accent);
}

.category-tab.no-drop {
    cursor: default;
}

.category-tab.no-drop.drag-over {
    background: var(--danger);
    color: var(--background);
    opacity: 0.7;
}

/* Mobile responsive category tabs */
@media (max-width: 768px) {
    .category-tabs {
        margin-bottom: 8px;
        -webkit-overflow-scrolling: touch;
    }

    .category-tab {
        padding: 6px 12px;
        font-size: 12px;
        min-width: 60px;
    }
}

/* Chart Indicator Styles */
.chart-indicator {
    display: flex;
    flex-direction: column;
    min-width: 120px;
    margin: 0 8px;
    font-family: 'Courier New', monospace;
}

.chart-indicator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.chart-indicator-label {
    color: var(--secondary);
    font-size: 9px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chart-indicator-value {
    font-weight: bold;
    font-size: 11px;
}

.chart-indicator-bar-container {
    position: relative;
    margin-bottom: 4px;
}

.chart-indicator-bar-background {
    width: 100%;
    height: 8px;
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.chart-indicator-bar-fill {
    height: 100%;
    border-radius: 2px;
    position: relative;
}

.chart-indicator-bar-marker {
    position: absolute;
    top: -2px;
    width: 2px;
    height: 12px;
    border-radius: 1px;
    transform: translateX(-50%);
}

.chart-indicator-scale {
    display: flex;
    justify-content: space-between;
    font-size: 8px;
    margin-top: 2px;
}

.chart-indicator-signal {
    text-align: center;
}

.chart-indicator-signal-text {
    font-size: 8px;
    font-weight: bold;
    text-transform: uppercase;
}

/* Mobile responsive chart indicators */
@media (max-width: 768px) {
    .chart-indicator {
        min-width: 80px;
        margin: 2px 4px;
    }

    .chart-indicator-label {
        font-size: 8px;
    }

    .chart-indicator-value {
        font-size: 10px;
    }

    .chart-indicator-bar-background {
        height: 6px;
    }

    .chart-indicator-bar-marker {
        height: 10px;
        top: -2px;
    }

    .chart-indicator-scale {
        font-size: 7px;
    }

    .chart-indicator-signal-text {
        font-size: 7px;
    }
}

/* Circular Gauge Styles */
.circular-gauge {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 8px;
    font-family: 'Courier New', monospace;
    flex: 1;
}

.gauge-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gauge-svg {
    position: absolute;
    top: 0;
    left: 0;
}

.gauge-progress {
    filter: drop-shadow(0 0 2px currentColor);
}

.gauge-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    z-index: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-90%, -50%);
}

.gauge-value {
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 1px;
}

.gauge-label {
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    line-height: 1;
    max-width: 75px;
    word-wrap: break-word;
    text-align: center;
}

/* Mobile responsive circular gauges */
@media (max-width: 768px) {
    .circular-gauge {
        margin: 0 4px;
    }

    .gauge-value {
        font-size: 12px;
    }

    .gauge-label {
        font-size: 6px;
        max-width: 40px;
    }
}
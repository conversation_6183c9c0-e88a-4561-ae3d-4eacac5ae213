{"name": "monitor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.7", "clsx": "^2.1.1", "graphql": "^16.11.0", "lucide-react": "^0.536.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}
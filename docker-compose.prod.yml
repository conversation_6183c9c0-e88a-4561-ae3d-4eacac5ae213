services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "4645:4645"
    env_file:
      - ./frontend/.env.production
    environment:
      - NEXT_PUBLIC_GRAPHQL_URL=https://monitor.dsserv.de/graphql
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - monitor-network
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    ports:
      - "4644:4644"
    env_file:
      - ./backend/.env.production
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/crypto-monitor
      - FRONTEND_URL=https://monitor.dsserv.de
      - NODE_ENV=production
    depends_on:
      - mongodb
    networks:
      - monitor-network
    restart: unless-stopped

  mongodb:
    image: mongo:7
    ports:
      - "27019:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - monitor-network
    restart: unless-stopped
    environment:
      - MONGO_INITDB_DATABASE=crypto-monitor

volumes:
  mongodb_data:

networks:
  monitor-network:
    driver: bridge
